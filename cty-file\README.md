# CTY-File 文件服务

## 📋 模块概述
文件服务负责处理平台所有的文件上传、存储、管理和访问，支持多种存储方式和文件类型。

## 🎯 核心功能

### 1. 文件上传
- **多格式支持**: 图片、文档、音频、视频等
- **大文件上传**: 分片上传和断点续传
- **批量上传**: 支持多文件同时上传
- **拖拽上传**: 前端拖拽上传体验

### 2. 存储管理
- **本地存储**: 服务器本地文件系统
- **MinIO存储**: 私有云对象存储
- **阿里云OSS**: 公有云对象存储
- **存储策略**: 根据文件类型选择存储方式

### 3. 文件处理
- **图片处理**: 压缩、裁剪、水印、格式转换
- **文档转换**: PDF转图片、文档预览
- **音视频处理**: 格式转换、压缩、截图
- **文件安全**: 病毒扫描、内容检测

### 4. 访问控制
- **权限验证**: 基于用户权限的访问控制
- **临时链接**: 生成有时效的访问链接
- **防盗链**: 防止外部盗用资源
- **CDN加速**: 内容分发网络加速

## 📁 文件分类管理

### 按用途分类
- **用户头像**: avatar/
- **身份证件**: idcard/
- **需求图片**: request/
- **聊天文件**: chat/
- **系统文件**: system/

### 按类型分类
- **图片文件**: jpg, jpeg, png, gif, bmp, webp
- **文档文件**: pdf, doc, docx, xls, xlsx, ppt, pptx
- **音频文件**: mp3, wav, aac, flac
- **视频文件**: mp4, avi, mov, wmv, flv

## 📊 数据模型

### 文件信息
```java
public class FileInfo {
    private String fileName;         // 文件名
    private String originalName;     // 原始文件名
    private String filePath;         // 文件路径
    private String fileUrl;          // 访问URL
    private String contentType;      // 文件类型
    private Long fileSize;           // 文件大小
    private String md5Hash;          // MD5哈希值
    private Integer storageType;     // 存储类型
    private Long uploadUserId;       // 上传用户ID
    private LocalDateTime uploadTime; // 上传时间
    // ... 其他字段
}
```

## 🔧 核心接口

### 1. 上传文件
```http
POST /file/upload
Content-Type: multipart/form-data

file: [binary data]
folder: common
```

### 2. 上传图片
```http
POST /file/upload/image
Content-Type: multipart/form-data

file: [image binary data]
```

### 3. 上传头像
```http
POST /file/upload/avatar
Content-Type: multipart/form-data

file: [image binary data]
```

### 4. 删除文件
```http
DELETE /file/delete?fileUrl=http://domain.com/files/xxx.jpg
```

### 5. 获取文件信息
```http
GET /file/info?fileName=xxx.jpg
```

## 🗄️ 存储配置

### 本地存储配置
```yaml
file:
  storage-type: local
  local:
    path: /data/files
    url-prefix: http://localhost:8087/files
```

### MinIO配置
```yaml
file:
  storage-type: minio
  minio:
    endpoint: http://localhost:9000
    access-key: minioadmin
    secret-key: minioadmin
    bucket-name: city-help
```

### 阿里云OSS配置
```yaml
file:
  storage-type: oss
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    access-key-id: your-access-key-id
    access-key-secret: your-access-key-secret
    bucket-name: city-help
```

## 🖼️ 图片处理功能

### 图片压缩
```java
/**
 * 图片压缩处理
 */
public String compressImage(MultipartFile file) {
    try {
        BufferedImage image = ImageIO.read(file.getInputStream());
        
        // 计算压缩尺寸
        int[] newSize = calculateCompressSize(image.getWidth(), image.getHeight());
        
        // 创建压缩后的图片
        BufferedImage compressedImage = new BufferedImage(
            newSize[0], newSize[1], BufferedImage.TYPE_INT_RGB
        );
        
        Graphics2D g2d = compressedImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, 
                           RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(image, 0, 0, newSize[0], newSize[1], null);
        g2d.dispose();
        
        // 保存压缩后的图片
        return saveCompressedImage(compressedImage, file.getOriginalFilename());
        
    } catch (IOException e) {
        throw new RuntimeException("图片压缩失败", e);
    }
}
```

### 添加水印
```java
/**
 * 添加水印
 */
public String addWatermark(String imageUrl, String watermarkText) {
    try {
        BufferedImage image = ImageIO.read(new URL(imageUrl));
        Graphics2D g2d = image.createGraphics();
        
        // 设置水印样式
        g2d.setColor(new Color(255, 255, 255, 128));
        g2d.setFont(new Font("Arial", Font.BOLD, 20));
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, 
                           RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 计算水印位置
        FontMetrics fm = g2d.getFontMetrics();
        int x = image.getWidth() - fm.stringWidth(watermarkText) - 10;
        int y = image.getHeight() - 10;
        
        // 绘制水印
        g2d.drawString(watermarkText, x, y);
        g2d.dispose();
        
        return saveWatermarkedImage(image);
        
    } catch (IOException e) {
        throw new RuntimeException("添加水印失败", e);
    }
}
```

## 🛡️ 安全机制

### 1. 文件类型检查
```java
/**
 * 检查文件类型
 */
public boolean isAllowedFileType(MultipartFile file, String category) {
    String contentType = file.getContentType();
    String fileName = file.getOriginalFilename();
    
    // 检查MIME类型
    if (!isAllowedMimeType(contentType, category)) {
        return false;
    }
    
    // 检查文件扩展名
    String extension = getFileExtension(fileName);
    if (!isAllowedExtension(extension, category)) {
        return false;
    }
    
    // 检查文件头
    return isValidFileHeader(file, extension);
}
```

### 2. 文件大小限制
```java
/**
 * 检查文件大小
 */
public boolean isAllowedFileSize(MultipartFile file, String category) {
    long fileSize = file.getSize();
    long maxSize = getMaxFileSize(category);
    
    return fileSize <= maxSize;
}
```

### 3. 病毒扫描
```java
/**
 * 病毒扫描
 */
public boolean scanVirus(MultipartFile file) {
    // 集成第三方病毒扫描引擎
    // 如ClamAV等
    return virusScanEngine.scan(file.getInputStream());
}
```

## 📈 文件统计

### 存储统计
- 总文件数量
- 存储空间使用
- 文件类型分布
- 上传频率统计

### 访问统计
- 文件访问次数
- 热门文件排行
- 下载流量统计
- CDN命中率

## 🔄 文件生命周期

### 临时文件清理
```java
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
public void cleanTempFiles() {
    // 清理超过7天的临时文件
    LocalDateTime expireTime = LocalDateTime.now().minusDays(7);
    List<FileInfo> expiredFiles = fileRepository.findTempFilesBefore(expireTime);
    
    for (FileInfo file : expiredFiles) {
        deleteFile(file.getFilePath());
        fileRepository.delete(file);
    }
}
```

### 冗余文件清理
```java
@Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
public void cleanOrphanFiles() {
    // 清理没有被引用的文件
    List<FileInfo> orphanFiles = fileRepository.findOrphanFiles();
    
    for (FileInfo file : orphanFiles) {
        deleteFile(file.getFilePath());
        fileRepository.delete(file);
    }
}
```

## 🚀 技术特性
- **端口**: 8087
- **存储**: 本地/MinIO/阿里云OSS
- **缓存**: Redis (database: 6)
- **图片处理**: Java 2D API
- **文件上传**: Spring Boot MultipartFile
- **CDN**: 阿里云CDN/腾讯云CDN

#!/bin/bash

echo "========================================"
echo "同城互助平台微服务启动脚本"
echo "========================================"

# 检查Java环境
echo ""
echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "Java环境未配置，请先安装JDK 8+"
    exit 1
fi
java -version

# 检查Maven环境
echo ""
echo "检查Maven环境..."
if ! command -v mvn &> /dev/null; then
    echo "Maven环境未配置，请先安装Maven 3.6+"
    exit 1
fi
mvn -version

echo ""
echo "========================================"
echo "开始启动微服务..."
echo "========================================"

# 获取脚本所在目录的父目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo ""
echo "[1/9] 启动网关服务 (端口: 8080)..."
cd "$PROJECT_DIR"
nohup mvn spring-boot:run -pl cty-gateway > logs/gateway.log 2>&1 &
sleep 10

echo ""
echo "[2/9] 启动认证服务 (端口: 8081)..."
nohup mvn spring-boot:run -pl cty-auth > logs/auth.log 2>&1 &
sleep 5

echo ""
echo "[3/9] 启动用户服务 (端口: 8082)..."
nohup mvn spring-boot:run -pl cty-user > logs/user.log 2>&1 &
sleep 5

echo ""
echo "[4/9] 启动订单服务 (端口: 8083)..."
nohup mvn spring-boot:run -pl cty-order > logs/order.log 2>&1 &
sleep 5

echo ""
echo "[5/9] 启动支付服务 (端口: 8084)..."
nohup mvn spring-boot:run -pl cty-payment > logs/payment.log 2>&1 &
sleep 5

echo ""
echo "[6/9] 启动消息服务 (端口: 8085)..."
nohup mvn spring-boot:run -pl cty-message > logs/message.log 2>&1 &
sleep 5

echo ""
echo "[7/9] 启动位置服务 (端口: 8086)..."
nohup mvn spring-boot:run -pl cty-location > logs/location.log 2>&1 &
sleep 5

echo ""
echo "[8/9] 启动文件服务 (端口: 8087)..."
nohup mvn spring-boot:run -pl cty-file > logs/file.log 2>&1 &
sleep 5

echo ""
echo "[9/9] 启动管理后台服务 (端口: 8088)..."
nohup mvn spring-boot:run -pl cty-admin > logs/admin.log 2>&1 &

echo ""
echo "========================================"
echo "所有微服务启动完成！"
echo "========================================"
echo ""
echo "服务访问地址："
echo "- 网关服务: http://localhost:8080"
echo "- API文档: http://localhost:8080/doc.html"
echo "- 认证服务: http://localhost:8081"
echo "- 用户服务: http://localhost:8082"
echo "- 订单服务: http://localhost:8083"
echo "- 支付服务: http://localhost:8084"
echo "- 消息服务: http://localhost:8085"
echo "- 位置服务: http://localhost:8086"
echo "- 文件服务: http://localhost:8087"
echo "- 管理后台: http://localhost:8088"
echo ""
echo "请确保以下基础服务已启动："
echo "- MySQL (端口: 3306)"
echo "- Redis (端口: 6379)"
echo "- RabbitMQ (端口: 5672)"
echo "- Nacos (端口: 8848)"
echo ""
echo "日志文件位置: $PROJECT_DIR/logs/"

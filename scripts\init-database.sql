-- 快速初始化用户表和测试数据

-- 如果用户表不存在，创建用户表
CREATE TABLE IF NOT EXISTS user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    nickname VARCHAR(50) COMMENT '昵称',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别 0-未知 1-男 2-女',
    birthday DATETIME COMMENT '生日',
    city VARCHAR(50) COMMENT '城市',
    address VARCHAR(255) COMMENT '地址',
    bio TEXT COMMENT '个人简介',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-正常 2-冻结',
    auth_status TINYINT DEFAULT 0 COMMENT '实名认证状态 0-未认证 1-认证中 2-已认证 3-认证失败',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(20) COMMENT '身份证号',
    credit_score INT DEFAULT 100 COMMENT '信用分数',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    version INT DEFAULT 1 COMMENT '版本号'
) COMMENT '用户表';

-- 清空现有测试数据（如果存在）
DELETE FROM user WHERE username LIKE 'user%';

-- 插入测试用户数据
INSERT INTO user (username, nickname, phone, email, city, status, auth_status, real_name, credit_score) VALUES 
('user001', '张三', '13800138001', '<EMAIL>', '北京市', 1, 2, '张三', 95),
('user002', '李四', '13800138002', '<EMAIL>', '上海市', 1, 1, '李四', 88),
('user003', '王五', '13800138003', '<EMAIL>', '广州市', 1, 2, '王五', 92),
('user004', '赵六', '13800138004', '<EMAIL>', '深圳市', 2, 0, NULL, 75),
('user005', '钱七', '13800138005', '<EMAIL>', '杭州市', 1, 2, '钱七', 98),
('user006', '孙八', '13800138006', '<EMAIL>', '成都市', 1, 0, NULL, 85),
('user007', '周九', '13800138007', '<EMAIL>', '武汉市', 1, 2, '周九', 90),
('user008', '吴十', '13800138008', '<EMAIL>', '西安市', 1, 1, '吴十', 87),
('user009', '郑十一', '13800138009', '<EMAIL>', '南京市', 1, 2, '郑十一', 93),
('user010', '王十二', '13800138010', '<EMAIL>', '重庆市', 1, 0, NULL, 82);

-- 查看插入结果
SELECT COUNT(*) as total_users FROM user WHERE deleted = 0;
SELECT status, COUNT(*) as count FROM user WHERE deleted = 0 GROUP BY status;
SELECT auth_status, COUNT(*) as count FROM user WHERE deleted = 0 GROUP BY auth_status;

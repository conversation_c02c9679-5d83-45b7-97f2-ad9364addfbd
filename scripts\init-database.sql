-- 快速初始化用户表和测试数据

-- 如果用户表不存在，创建用户表
CREATE TABLE IF NOT EXISTS user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    nickname VARCHAR(50) COMMENT '昵称',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别 0-未知 1-男 2-女',
    birthday DATETIME COMMENT '生日',
    city VARCHAR(50) COMMENT '城市',
    address VARCHAR(255) COMMENT '地址',
    bio TEXT COMMENT '个人简介',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-正常 2-冻结',
    auth_status TINYINT DEFAULT 0 COMMENT '实名认证状态 0-未认证 1-认证中 2-已认证 3-认证失败',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(20) COMMENT '身份证号',
    credit_score INT DEFAULT 100 COMMENT '信用分数',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    version INT DEFAULT 1 COMMENT '版本号'
) COMMENT '用户表';

-- 清空现有测试数据（如果存在）
DELETE FROM user WHERE username LIKE 'user%';

-- 插入测试用户数据
INSERT INTO user (username, nickname, phone, email, city, status, auth_status, real_name, credit_score) VALUES 
('user001', '张三', '13800138001', '<EMAIL>', '北京市', 1, 2, '张三', 95),
('user002', '李四', '13800138002', '<EMAIL>', '上海市', 1, 1, '李四', 88),
('user003', '王五', '13800138003', '<EMAIL>', '广州市', 1, 2, '王五', 92),
('user004', '赵六', '13800138004', '<EMAIL>', '深圳市', 2, 0, NULL, 75),
('user005', '钱七', '13800138005', '<EMAIL>', '杭州市', 1, 2, '钱七', 98),
('user006', '孙八', '13800138006', '<EMAIL>', '成都市', 1, 0, NULL, 85),
('user007', '周九', '13800138007', '<EMAIL>', '武汉市', 1, 2, '周九', 90),
('user008', '吴十', '13800138008', '<EMAIL>', '西安市', 1, 1, '吴十', 87),
('user009', '郑十一', '13800138009', '<EMAIL>', '南京市', 1, 2, '郑十一', 93),
('user010', '王十二', '13800138010', '<EMAIL>', '重庆市', 1, 0, NULL, 82);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    provider_id BIGINT COMMENT '服务提供者ID',
    service_type TINYINT DEFAULT 1 COMMENT '服务类型 1-跑腿 2-家政 3-维修 4-其他',
    title VARCHAR(200) NOT NULL COMMENT '订单标题',
    description TEXT COMMENT '订单描述',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    paid_amount DECIMAL(10,2) COMMENT '实际支付金额',
    status TINYINT DEFAULT 1 COMMENT '订单状态 1-待支付 2-已支付 3-进行中 4-待确认 5-已完成 6-已取消 7-已退款',
    payment_method TINYINT COMMENT '支付方式 1-微信 2-支付宝 3-余额',
    payment_time DATETIME COMMENT '支付时间',
    service_address VARCHAR(500) COMMENT '服务地址',
    contact_name VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    appointment_time DATETIME COMMENT '预约时间',
    start_time DATETIME COMMENT '开始时间',
    finish_time DATETIME COMMENT '完成时间',
    cancel_time DATETIME COMMENT '取消时间',
    cancel_reason VARCHAR(500) COMMENT '取消原因',
    rating TINYINT COMMENT '评价分数',
    comment TEXT COMMENT '评价内容',
    remark VARCHAR(500) COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    version INT DEFAULT 1 COMMENT '版本号'
) COMMENT '订单表';

-- 清空现有测试订单数据（如果存在）
DELETE FROM orders WHERE order_no LIKE 'ORD%';

-- 插入测试订单数据
INSERT INTO orders (order_no, user_id, provider_id, service_type, title, description, amount, paid_amount, status, payment_method, payment_time, service_address, contact_name, contact_phone, appointment_time) VALUES
('ORD202401010001', 1, 2, 1, '帮忙代买生活用品', '需要代买一些日用品，包括洗发水、牙膏等', 25.00, 25.00, 5, 1, '2024-01-01 10:30:00', '北京市朝阳区某小区', '张三', '13800138001', '2024-01-01 14:00:00'),
('ORD202401010002', 3, 4, 2, '家庭保洁服务', '需要进行全屋深度清洁，包括厨房和卫生间', 120.00, 120.00, 3, 2, '2024-01-01 11:00:00', '上海市浦东新区某公寓', '李四', '13800138003', '2024-01-01 15:00:00'),
('ORD202401010003', 5, 6, 3, '空调维修', '空调不制冷，需要专业维修', 80.00, 80.00, 5, 1, '2024-01-01 12:00:00', '广州市天河区某写字楼', '王五', '13800138005', '2024-01-01 16:00:00'),
('ORD202401010004', 7, NULL, 1, '文件快递', '需要紧急送达重要文件', 15.00, NULL, 1, NULL, NULL, '深圳市南山区科技园', '赵六', '13800138007', '2024-01-02 09:00:00'),
('ORD202401010005', 8, 9, 4, '宠物寄养', '出差期间需要寄养小狗3天', 150.00, 150.00, 4, 3, '2024-01-01 13:00:00', '杭州市西湖区某小区', '钱七', '13800138008', '2024-01-02 08:00:00'),
('ORD202401010006', 10, 1, 2, '搬家服务', '小件搬家，从老房子搬到新房子', 200.00, 200.00, 5, 2, '2024-01-01 14:00:00', '成都市锦江区', '孙八', '13800138010', '2024-01-02 10:00:00'),
('ORD202401010007', 2, NULL, 1, '代取快递', '帮忙代取几个快递包裹', 10.00, NULL, 6, NULL, NULL, '武汉市武昌区某小区', '周九', '13800138002', '2024-01-02 11:00:00'),
('ORD202401010008', 4, 5, 3, '电脑维修', '笔记本电脑开不了机，需要检修', 100.00, 100.00, 3, 1, '2024-01-01 15:00:00', '西安市雁塔区', '吴十', '13800138004', '2024-01-02 14:00:00');

-- 查看插入结果
SELECT COUNT(*) as total_users FROM user WHERE deleted = 0;
SELECT status, COUNT(*) as count FROM user WHERE deleted = 0 GROUP BY status;
SELECT auth_status, COUNT(*) as count FROM user WHERE deleted = 0 GROUP BY auth_status;

SELECT COUNT(*) as total_orders FROM orders WHERE deleted = 0;
SELECT status, COUNT(*) as count FROM orders WHERE deleted = 0 GROUP BY status;
SELECT service_type, COUNT(*) as count FROM orders WHERE deleted = 0 GROUP BY service_type;

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cty.admin.mapper.CityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cty.admin.entity.City">
        <id column="id" property="id" />
        <result column="city_code" property="cityCode" />
        <result column="city_name" property="cityName" />
        <result column="parent_id" property="parentId" />
        <result column="level" property="level" />
        <result column="full_name" property="fullName" />
        <result column="short_name" property="shortName" />
        <result column="pinyin" property="pinyin" />
        <result column="pinyin_prefix" property="pinyinPrefix" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="sort" property="sort" />
        <result column="status" property="status" />
        <result column="service_enabled" property="serviceEnabled" />
        <result column="service_radius" property="serviceRadius" />
        <result column="description" property="description" />
        <result column="icon" property="icon" />
        <result column="is_hot" property="isHot" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, city_code, city_name, parent_id, level, full_name, short_name, pinyin, pinyin_prefix,
        longitude, latitude, sort, status, service_enabled, service_radius, description, icon, is_hot,
        create_time, update_time, create_by, update_by, deleted, version
    </sql>

    <!-- 分页查询城市列表 -->
    <select id="selectCityPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM city
        WHERE deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (city_name LIKE CONCAT('%', #{keyword}, '%') 
                 OR city_code LIKE CONCAT('%', #{keyword}, '%')
                 OR pinyin LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="level != null">
            AND level = #{level}
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据父级ID查询子城市 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM city
        WHERE deleted = 0 AND parent_id = #{parentId} AND status = 1
        ORDER BY sort ASC, city_name ASC
    </select>

    <!-- 查询热门城市 -->
    <select id="selectHotCities" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM city
        WHERE deleted = 0 AND status = 1 AND is_hot = 1
        ORDER BY sort ASC, city_name ASC
    </select>

    <!-- 查询已开通服务的城市 -->
    <select id="selectServiceEnabledCities" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM city
        WHERE deleted = 0 AND status = 1 AND service_enabled = 1
        ORDER BY sort ASC, city_name ASC
    </select>

</mapper>

import request from '@/utils/request'

// 管理员登录
export function login(data) {
  return request({
    url: '/admin/user/login',
    method: 'post',
    data: data
  })
}

// 获取管理员信息
export function getUserInfo(username) {
  return request({
    url: '/admin/user/info',
    method: 'get',
    params: { username }
  })
}

// 管理员登出
export function logout() {
  return request({
    url: '/admin/user/logout',
    method: 'post'
  })
}

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/admin/user/list',
    method: 'get',
    params
  })
}

// 用户实名认证审核
export function auditUser(data) {
  return request({
    url: '/admin/user/audit',
    method: 'post',
    params: data
  })
}

// 冻结/解冻用户
export function freezeUser(data) {
  return request({
    url: '/admin/user/freeze',
    method: 'post',
    params: data
  })
}

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/admin/user/orders',
    method: 'get',
    params
  })
}

// 获取统计数据
export function getStatistics(params) {
  return request({
    url: '/admin/user/statistics',
    method: 'get',
    params
  })
}

package com.cty.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cty.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user")
public class User extends BaseEntity {
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 昵称
     */
    private String nickname;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 性别 0-未知 1-男 2-女
     */
    private Integer gender;
    
    /**
     * 生日
     */
    private LocalDateTime birthday;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 个人简介
     */
    private String bio;
    
    /**
     * 状态 0-禁用 1-正常 2-冻结
     */
    private Integer status;
    
    /**
     * 实名认证状态 0-未认证 1-认证中 2-已认证 3-认证失败
     */
    private Integer authStatus;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 身份证号
     */
    private String idCard;
    
    /**
     * 信用分数
     */
    private Integer creditScore;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    private String lastLoginIp;
}

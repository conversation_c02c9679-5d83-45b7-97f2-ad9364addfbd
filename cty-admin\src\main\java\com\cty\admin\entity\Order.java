package com.cty.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cty.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("orders")
public class Order extends BaseEntity {
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 服务提供者ID
     */
    private Long providerId;
    
    /**
     * 服务类型 1-跑腿 2-家政 3-维修 4-其他
     */
    private Integer serviceType;
    
    /**
     * 订单标题
     */
    private String title;
    
    /**
     * 订单描述
     */
    private String description;
    
    /**
     * 订单金额
     */
    private BigDecimal amount;
    
    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;
    
    /**
     * 订单状态 1-待支付 2-已支付 3-进行中 4-待确认 5-已完成 6-已取消 7-已退款
     */
    private Integer status;
    
    /**
     * 支付方式 1-微信 2-支付宝 3-余额
     */
    private Integer paymentMethod;
    
    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;
    
    /**
     * 服务地址
     */
    private String serviceAddress;
    
    /**
     * 联系人
     */
    private String contactName;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 预约时间
     */
    private LocalDateTime appointmentTime;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    
    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;
    
    /**
     * 取消原因
     */
    private String cancelReason;
    
    /**
     * 评价分数
     */
    private Integer rating;
    
    /**
     * 评价内容
     */
    private String comment;
    
    /**
     * 备注
     */
    private String remark;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cty.admin.mapper.OrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cty.admin.entity.Order">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="user_id" property="userId" />
        <result column="provider_id" property="providerId" />
        <result column="service_type" property="serviceType" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="amount" property="amount" />
        <result column="paid_amount" property="paidAmount" />
        <result column="status" property="status" />
        <result column="payment_method" property="paymentMethod" />
        <result column="payment_time" property="paymentTime" />
        <result column="service_address" property="serviceAddress" />
        <result column="contact_name" property="contactName" />
        <result column="contact_phone" property="contactPhone" />
        <result column="appointment_time" property="appointmentTime" />
        <result column="start_time" property="startTime" />
        <result column="finish_time" property="finishTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="rating" property="rating" />
        <result column="comment" property="comment" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, user_id, provider_id, service_type, title, description, amount, paid_amount, 
        status, payment_method, payment_time, service_address, contact_name, contact_phone, 
        appointment_time, start_time, finish_time, cancel_time, cancel_reason, rating, comment, remark,
        create_time, update_time, create_by, update_by, deleted, version
    </sql>

    <!-- 分页查询订单列表 -->
    <select id="selectOrderPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM orders
        WHERE deleted = 0
        <if test="orderNo != null and orderNo != ''">
            AND order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="startTime != null and startTime != ''">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>

<template>
  <PageContainer>
    <div class="cities-page">
      <!-- 搜索区域 -->
      <el-card class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input v-model="searchForm.keyword" placeholder="请输入城市名称或编码" clearable />
          </el-form-item>
          <el-form-item label="城市级别">
            <el-select v-model="searchForm.level" placeholder="请选择级别" clearable>
              <el-option label="省份" :value="1" />
              <el-option label="城市" :value="2" />
              <el-option label="区县" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchCityList">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button type="success" @click="showAddDialog">添加城市</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 城市列表 -->
      <el-card>
        <div class="table-container">
          <el-table :data="cityList" style="width: 100%" v-loading="loading">
            <el-table-column prop="cityCode" label="城市编码" width="120" />
            <el-table-column prop="cityName" label="城市名称" width="150" />
            <el-table-column prop="level" label="级别" width="80">
              <template #default="scope">
                <el-tag :type="getLevelType(scope.row.level)">
                  {{ getLevelText(scope.row.level) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="fullName" label="全名" width="200" />
            <el-table-column prop="pinyin" label="拼音" width="150" />
            <el-table-column prop="sort" label="排序" width="80" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  :active-value="1"
                  :inactive-value="0"
                  @change="updateStatus(scope.row.id, scope.row.status)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="serviceEnabled" label="服务状态" width="100">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.serviceEnabled"
                  :active-value="1"
                  :inactive-value="0"
                  @change="updateServiceStatus(scope.row.id, scope.row.serviceEnabled)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="isHot" label="热门" width="80">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.isHot"
                  :active-value="1"
                  :inactive-value="0"
                  @change="updateHotStatus(scope.row.id, scope.row.isHot)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <el-button type="primary" size="small" @click="editCity(scope.row)">编辑</el-button>
                <el-button type="danger" size="small" @click="deleteCity(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchCityList"
            @current-change="fetchCityList"
          />
        </div>
      </el-card>

      <!-- 添加/编辑城市对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="600px"
        @close="resetForm"
      >
        <el-form :model="cityForm" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item label="城市编码" prop="cityCode">
            <el-input v-model="cityForm.cityCode" placeholder="请输入城市编码" />
          </el-form-item>
          <el-form-item label="城市名称" prop="cityName">
            <el-input v-model="cityForm.cityName" placeholder="请输入城市名称" />
          </el-form-item>
          <el-form-item label="城市级别" prop="level">
            <el-select v-model="cityForm.level" placeholder="请选择级别">
              <el-option label="省份" :value="1" />
              <el-option label="城市" :value="2" />
              <el-option label="区县" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="父级城市" prop="parentId" v-if="cityForm.level > 1">
            <el-select v-model="cityForm.parentId" placeholder="请选择父级城市">
              <el-option
                v-for="city in parentCities"
                :key="city.id"
                :label="city.cityName"
                :value="city.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="城市全名" prop="fullName">
            <el-input v-model="cityForm.fullName" placeholder="请输入城市全名" />
          </el-form-item>
          <el-form-item label="拼音" prop="pinyin">
            <el-input v-model="cityForm.pinyin" placeholder="请输入拼音" />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="cityForm.sort" :min="0" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCity">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PageContainer from '@/components/PageContainer.vue'
import {
  getCityList,
  addCity,
  updateCity,
  deleteCity as deleteCityApi,
  updateCityStatus,
  updateServiceStatus as updateServiceStatusApi,
  updateHotStatus as updateHotStatusApi
} from '@/api/city'

// 响应式数据
const loading = ref(false)
const cityList = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const parentCities = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  level: null,
  status: null
})

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 城市表单
const cityForm = reactive({
  id: null,
  cityCode: '',
  cityName: '',
  level: 1,
  parentId: 0,
  fullName: '',
  pinyin: '',
  sort: 0
})

// 表单验证规则
const rules = {
  cityCode: [{ required: true, message: '请输入城市编码', trigger: 'blur' }],
  cityName: [{ required: true, message: '请输入城市名称', trigger: 'blur' }],
  level: [{ required: true, message: '请选择城市级别', trigger: 'change' }]
}

// 获取城市列表
const fetchCityList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    const response = await getCityList(params)
    if (response.code === 200) {
      cityList.value = response.data.records
      pagination.total = response.data.total
    }
  } catch (error) {
    ElMessage.error('获取城市列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    level: null,
    status: null
  })
  pagination.pageNum = 1
  fetchCityList()
}

// 获取级别类型
const getLevelType = (level) => {
  const typeMap = { 1: 'success', 2: 'primary', 3: 'info' }
  return typeMap[level] || 'info'
}

// 获取级别文本
const getLevelText = (level) => {
  const textMap = { 1: '省份', 2: '城市', 3: '区县' }
  return textMap[level] || '未知'
}

// 更新状态
const updateStatus = async (id, status) => {
  try {
    await updateCityStatus({ id, status })
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    fetchCityList() // 刷新列表恢复原状态
  }
}

// 更新服务状态
const updateServiceStatus = async (id, serviceEnabled) => {
  try {
    await updateServiceStatusApi({ id, serviceEnabled })
    ElMessage.success('服务状态更新成功')
  } catch (error) {
    ElMessage.error('服务状态更新失败')
    fetchCityList()
  }
}

// 更新热门状态
const updateHotStatus = async (id, isHot) => {
  try {
    await updateHotStatusApi({ id, isHot })
    ElMessage.success('热门状态更新成功')
  } catch (error) {
    ElMessage.error('热门状态更新失败')
    fetchCityList()
  }
}

// 显示添加对话框
const showAddDialog = () => {
  dialogTitle.value = '添加城市'
  dialogVisible.value = true
}

// 编辑城市
const editCity = (city) => {
  dialogTitle.value = '编辑城市'
  Object.assign(cityForm, city)
  dialogVisible.value = true
}

// 保存城市
const saveCity = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (cityForm.id) {
      await updateCity(cityForm)
      ElMessage.success('更新成功')
    } else {
      await addCity(cityForm)
      ElMessage.success('添加成功')
    }
    
    dialogVisible.value = false
    fetchCityList()
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 删除城市
const deleteCity = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这个城市吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteCityApi(id)
    ElMessage.success('删除成功')
    fetchCityList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(cityForm, {
    id: null,
    cityCode: '',
    cityName: '',
    level: 1,
    parentId: 0,
    fullName: '',
    pinyin: '',
    sort: 0
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchCityList()
})
</script>

<style lang="scss" scoped>
.cities-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .search-area {
    margin-bottom: 20px;
    flex-shrink: 0;
  }
  
  .table-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .el-table {
      flex: 1;
    }
    
    .el-pagination {
      margin-top: 20px;
      text-align: right;
      flex-shrink: 0;
    }
  }
}
</style>

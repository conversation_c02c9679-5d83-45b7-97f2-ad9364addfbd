var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name2 in all)
    __defProp(target, name2, { get: all[name2], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var stdin_exports = {};
__export(stdin_exports, {
  default: () => stdin_default,
  notifyProps: () => notifyProps
});
module.exports = __toCommonJS(stdin_exports);
var import_vue = require("vue");
var import_utils = require("../utils");
var import_popup = require("../popup");
var import_shared = require("../popup/shared");
const [name, bem] = (0, import_utils.createNamespace)("notify");
const popupInheritProps = ["lockScroll", "position", "show", "teleport", "zIndex"];
const notifyProps = (0, import_utils.extend)({}, import_shared.popupSharedProps, {
  type: (0, import_utils.makeStringProp)("danger"),
  color: String,
  message: import_utils.numericProp,
  position: (0, import_utils.makeStringProp)("top"),
  className: import_utils.unknownProp,
  background: String,
  lockScroll: Boolean
});
var stdin_default = (0, import_vue.defineComponent)({
  name,
  props: notifyProps,
  emits: ["update:show"],
  setup(props, {
    emit,
    slots
  }) {
    const updateShow = (show) => emit("update:show", show);
    return () => (0, import_vue.createVNode)(import_popup.Popup, (0, import_vue.mergeProps)({
      "class": [bem([props.type]), props.className],
      "style": {
        color: props.color,
        background: props.background
      },
      "overlay": false,
      "duration": 0.2,
      "onUpdate:show": updateShow
    }, (0, import_utils.pick)(props, popupInheritProps)), {
      default: () => [slots.default ? slots.default() : props.message]
    });
  }
});

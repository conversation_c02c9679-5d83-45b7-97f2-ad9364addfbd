server:
  port: 8088

spring:
  application:
    name: cty-admin
  profiles:
    active: dev
  config:
    import:
      - optional:nacos:cty-admin.yml
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
        namespace: dev
      config:
        server-addr: **************:8848
        namespace: dev
        file-extension: yml
        group: DEFAULT_GROUP

  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************************
    username: root
    password: 123456

  redis:
    host: **************
    port: 6379
    password: 123456
    database: 7
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  security:
    user:
      name: admin
      password: admin123

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  mapper-locations: classpath*:mapper/**/*Mapper.xml

# JWT配置
jwt:
  secret: cty-admin-secret-key-2023
  expiration: 86400 # 24小时

logging:
  level:
    com.cty.admin: debug
    com.baomidou.mybatisplus: debug

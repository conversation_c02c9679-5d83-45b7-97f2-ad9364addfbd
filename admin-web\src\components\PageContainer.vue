<template>
  <div class="page-container">
    <slot></slot>
  </div>
</template>

<script setup>
// 通用页面容器组件
// 用于统一页面布局和样式
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  overflow: hidden;
  
  // 确保内容区域能够正确滚动
  :deep(.el-card) {
    margin-bottom: 20px;
    flex-shrink: 0;
    
    &:last-child {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .el-card__body {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }
    }
  }
  
  // 表格容器样式
  :deep(.table-container) {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .el-table {
      flex: 1;
    }
    
    .el-pagination {
      margin-top: 20px;
      text-align: right;
      flex-shrink: 0;
    }
  }
  
  // 搜索区域样式
  :deep(.search-area) {
    flex-shrink: 0;
    margin-bottom: 20px;
  }
  
  // 图表容器样式
  :deep(.chart-container) {
    height: 300px;
    min-height: 300px;
  }
}
</style>

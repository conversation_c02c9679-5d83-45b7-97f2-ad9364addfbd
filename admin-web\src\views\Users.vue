<template>
  <div class="users-page">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入用户名或昵称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="认证状态">
            <el-select v-model="searchForm.authStatus" placeholder="请选择认证状态" clearable>
              <el-option label="未认证" :value="0" />
              <el-option label="认证中" :value="1" />
              <el-option label="已认证" :value="2" />
              <el-option label="认证失败" :value="3" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 用户列表 -->
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="头像" width="80">
          <template #default="scope">
            <el-avatar :src="scope.row.avatar" :size="40">
              {{ scope.row.nickname?.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>
        
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="nickname" label="昵称" />
        <el-table-column prop="phone" label="手机号" />
        
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="认证状态" width="100">
          <template #default="scope">
            <el-tag :type="getAuthStatusType(scope.row.authStatus)">
              {{ getAuthStatusText(scope.row.authStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="注册时间" width="180" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            
            <el-button
              :type="scope.row.status === 1 ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑用户"
      width="600px"
      @close="handleEditDialogClose"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" disabled />
        </el-form-item>
        
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getUserList, freezeUser } from '@/api/user'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const userList = ref([])
const selectedUsers = ref([])
const editDialogVisible = ref(false)
const editFormRef = ref()

const searchForm = reactive({
  keyword: '',
  status: null,
  authStatus: null
})

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

const editForm = reactive({
  id: null,
  username: '',
  nickname: '',
  phone: '',
  status: 1
})

const editRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 获取认证状态类型
const getAuthStatusType = (status) => {
  const statusMap = {
    0: 'info',     // 未认证
    1: 'warning',  // 认证中
    2: 'success',  // 已认证
    3: 'danger'    // 认证失败
  }
  return statusMap[status] || 'info'
}

// 获取认证状态文本
const getAuthStatusText = (status) => {
  const statusMap = {
    0: '未认证',
    1: '认证中',
    2: '已认证',
    3: '认证失败'
  }
  return statusMap[status] || '未知'
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getUserList(params)
    if (response.code === 200) {
      userList.value = response.data.records
      pagination.total = response.data.total
    }
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  fetchUserList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: null,
    authStatus: null
  })
  pagination.pageNum = 1
  fetchUserList()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 编辑用户
const handleEdit = (row) => {
  Object.assign(editForm, {
    id: row.id,
    username: row.username,
    nickname: row.nickname,
    phone: row.phone,
    status: row.status
  })
  editDialogVisible.value = true
}

// 切换用户状态
const handleToggleStatus = async (row) => {
  const action = row.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}该用户吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await freezeUser({
      userId: row.id,
      status: row.status === 1 ? 0 : 1,
      reason: `管理员${action}用户`
    })
    
    if (response.code === 200) {
      ElMessage.success(`${action}成功`)
      fetchUserList()
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该用户吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    // TODO: 调用删除接口
    ElMessage.success('删除成功')
    fetchUserList()
  } catch (error) {
    // 用户取消操作
  }
}

// 编辑对话框关闭
const handleEditDialogClose = () => {
  editFormRef.value?.resetFields()
}

// 提交编辑
const handleEditSubmit = async () => {
  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return
    
    // TODO: 调用编辑接口
    ElMessage.success('编辑成功')
    editDialogVisible.value = false
    fetchUserList()
  } catch (error) {
    ElMessage.error('编辑失败')
  }
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  fetchUserList()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchUserList()
}

onMounted(() => {
  fetchUserList()
})
</script>

<style lang="scss" scoped>
.users-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-area {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    flex-shrink: 0;
    border-radius: 4px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style>

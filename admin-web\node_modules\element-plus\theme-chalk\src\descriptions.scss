@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

$descriptions-header-margin-bottom: () !default;
$descriptions-header-margin-bottom: map.merge(
  (
    'large': 20px,
    'default': 16px,
    'small': 12px,
  ),
  $descriptions-header-margin-bottom
);

$descriptions-title-font-size: () !default;
$descriptions-title-font-size: map.merge(
  (
    'large': 16px,
    'default': 16px,
    'small': 14px,
  ),
  $descriptions-title-font-size
);

$descriptions-cell-padding-bottom: () !default;
$descriptions-cell-padding-bottom: map.merge(
  (
    'large': 16px,
    'default': 12px,
    'small': 8px,
  ),
  $descriptions-cell-padding-bottom
);

$descriptions-bordered-cell-padding: () !default;
$descriptions-bordered-cell-padding: map.merge(
  (
    'large': 12px 15px,
    'default': 8px 11px,
    'small': 4px 7px,
  ),
  $descriptions-bordered-cell-padding
);

$descriptions-cell-font-size: () !default;
$descriptions-cell-font-size: map.merge(
  (
    'large': 14px,
    'default': 14px,
    'small': 12px,
  ),
  $descriptions-cell-font-size
);

@include b(descriptions) {
  @include set-component-css-var('descriptions', $descriptions);

  box-sizing: border-box;
  font-size: getCssVar('font-size', 'base');
  color: getCssVar('text-color', 'primary');

  @include e(header) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: map.get($descriptions-header-margin-bottom, 'default');

    @include e(title) {
      color: getCssVar('text-color', 'primary');
      font-size: map.get($descriptions-title-font-size, 'default');
      font-weight: bold;
    }
  }

  @include e(body) {
    background-color: getCssVar('fill-color', 'blank');

    .#{$namespace}-descriptions__table {
      border-collapse: collapse;
      width: 100%;

      .#{$namespace}-descriptions__cell {
        box-sizing: border-box;
        text-align: left;
        line-height: 23px;
        font-size: map.get($descriptions-cell-font-size, 'default');

        @include when(left) {
          text-align: left;
        }

        @include when(center) {
          text-align: center;
        }

        @include when(right) {
          text-align: right;
        }
      }

      &.is-bordered {
        .#{$namespace}-descriptions__cell {
          border: getCssVar('descriptions-table-border');
          padding: map.get($descriptions-bordered-cell-padding, 'default');
        }
      }

      &:not(.is-bordered) {
        .#{$namespace}-descriptions__cell {
          padding-bottom: map.get($descriptions-cell-padding-bottom, 'default');
        }
      }
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      font-size: map.get($input-font-size, $size);

      @include e(header) {
        margin-bottom: map.get($descriptions-header-margin-bottom, $size);

        @include e(title) {
          font-size: map.get($descriptions-title-font-size, $size);
        }
      }

      @include e(body) {
        .#{$namespace}-descriptions__table {
          .#{$namespace}-descriptions__cell {
            font-size: map.get($descriptions-cell-font-size, $size);
          }

          &.is-bordered {
            .#{$namespace}-descriptions__cell {
              padding: map.get($descriptions-bordered-cell-padding, $size);
            }
          }

          &:not(.is-bordered) {
            .#{$namespace}-descriptions__cell {
              padding-bottom: map.get($descriptions-cell-padding-bottom, $size);
            }
          }
        }
      }
    }
  }
}

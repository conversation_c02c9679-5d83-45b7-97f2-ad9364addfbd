-- 创建订单表的独立脚本
-- 使用数据库
USE city_help_platform;

-- 删除已存在的orders表（如果存在）
DROP TABLE IF EXISTS orders;

-- 创建订单表
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    provider_id BIGINT COMMENT '服务提供者ID',
    service_type TINYINT DEFAULT 1 COMMENT '服务类型 1-跑腿 2-家政 3-维修 4-其他',
    title VARCHAR(200) NOT NULL COMMENT '订单标题',
    description TEXT COMMENT '订单描述',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    paid_amount DECIMAL(10,2) COMMENT '实际支付金额',
    status TINYINT DEFAULT 1 COMMENT '订单状态 1-待支付 2-已支付 3-进行中 4-待确认 5-已完成 6-已取消 7-已退款',
    payment_method TINYINT COMMENT '支付方式 1-微信 2-支付宝 3-余额',
    payment_time DATETIME COMMENT '支付时间',
    service_address VARCHAR(500) COMMENT '服务地址',
    contact_name VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    appointment_time DATETIME COMMENT '预约时间',
    start_time DATETIME COMMENT '开始时间',
    finish_time DATETIME COMMENT '完成时间',
    cancel_time DATETIME COMMENT '取消时间',
    cancel_reason VARCHAR(500) COMMENT '取消原因',
    rating TINYINT COMMENT '评价分数',
    comment TEXT COMMENT '评价内容',
    remark VARCHAR(500) COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    version INT DEFAULT 1 COMMENT '版本号',
    
    -- 添加索引
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    INDEX idx_deleted (deleted)
) COMMENT '订单表' ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入测试订单数据
INSERT INTO orders (order_no, user_id, provider_id, service_type, title, description, amount, paid_amount, status, payment_method, payment_time, service_address, contact_name, contact_phone, appointment_time) VALUES 
('ORD202401010001', 1, 2, 1, '帮忙代买生活用品', '需要代买一些日用品，包括洗发水、牙膏等', 25.00, 25.00, 5, 1, '2024-01-01 10:30:00', '北京市朝阳区某小区', '张三', '13800138001', '2024-01-01 14:00:00'),
('ORD202401010002', 3, 4, 2, '家庭保洁服务', '需要进行全屋深度清洁，包括厨房和卫生间', 120.00, 120.00, 3, 2, '2024-01-01 11:00:00', '上海市浦东新区某公寓', '李四', '13800138003', '2024-01-01 15:00:00'),
('ORD202401010003', 5, 6, 3, '空调维修', '空调不制冷，需要专业维修', 80.00, 80.00, 5, 1, '2024-01-01 12:00:00', '广州市天河区某写字楼', '王五', '13800138005', '2024-01-01 16:00:00'),
('ORD202401010004', 7, NULL, 1, '文件快递', '需要紧急送达重要文件', 15.00, NULL, 1, NULL, NULL, '深圳市南山区科技园', '赵六', '13800138007', '2024-01-02 09:00:00'),
('ORD202401010005', 8, 9, 4, '宠物寄养', '出差期间需要寄养小狗3天', 150.00, 150.00, 4, 3, '2024-01-01 13:00:00', '杭州市西湖区某小区', '钱七', '13800138008', '2024-01-02 08:00:00'),
('ORD202401010006', 10, 1, 2, '搬家服务', '小件搬家，从老房子搬到新房子', 200.00, 200.00, 5, 2, '2024-01-01 14:00:00', '成都市锦江区', '孙八', '13800138010', '2024-01-02 10:00:00'),
('ORD202401010007', 2, NULL, 1, '代取快递', '帮忙代取几个快递包裹', 10.00, NULL, 6, NULL, NULL, '武汉市武昌区某小区', '周九', '13800138002', '2024-01-02 11:00:00'),
('ORD202401010008', 4, 5, 3, '电脑维修', '笔记本电脑开不了机，需要检修', 100.00, 100.00, 3, 1, '2024-01-01 15:00:00', '西安市雁塔区', '吴十', '13800138004', '2024-01-02 14:00:00'),
('ORD202401010009', 1, 3, 1, '超市购物代买', '代买一周的生活用品和食材', 180.00, 180.00, 5, 1, '2024-01-02 09:30:00', '北京市海淀区某小区', '张三', '13800138001', '2024-01-02 16:00:00'),
('ORD202401010010', 6, 7, 2, '办公室清洁', '公司办公室日常清洁服务', 300.00, 300.00, 5, 2, '2024-01-02 10:00:00', '上海市静安区某写字楼', '李明', '13800138006', '2024-01-03 08:00:00'),
('ORD202401010011', 9, NULL, 4, '宠物美容', '给小狗洗澡和美容', 80.00, NULL, 2, NULL, NULL, '广州市番禺区宠物店', '王丽', '13800138009', '2024-01-03 10:00:00'),
('ORD202401010012', 5, 8, 3, '洗衣机维修', '洗衣机不能脱水，需要维修', 120.00, 120.00, 4, 3, '2024-01-02 11:30:00', '深圳市福田区某小区', '王五', '13800138005', '2024-01-03 14:00:00');

-- 查看创建结果
SELECT '订单表创建完成' as message;
SELECT COUNT(*) as total_orders FROM orders WHERE deleted = 0;
SELECT status, COUNT(*) as count FROM orders WHERE deleted = 0 GROUP BY status ORDER BY status;
SELECT service_type, COUNT(*) as count FROM orders WHERE deleted = 0 GROUP BY service_type ORDER BY service_type;

-- 显示最新的几条订单
SELECT order_no, title, amount, status, create_time FROM orders WHERE deleted = 0 ORDER BY create_time DESC LIMIT 5;

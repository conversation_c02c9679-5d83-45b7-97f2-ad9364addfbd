package com.cty.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cty.admin.entity.City;
import com.cty.admin.service.CityService;
import com.cty.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 城市管理控制器
 */
@Tag(name = "城市管理", description = "城市管理相关接口")
@RestController
@RequestMapping("/city")
@RequiredArgsConstructor
public class CityController {

    private final CityService cityService;

    @Operation(summary = "获取城市列表")
    @GetMapping("/list")
    public Result<Page<City>> getCityList(@RequestParam(defaultValue = "1") Integer pageNum,
                                         @RequestParam(defaultValue = "10") Integer pageSize,
                                         @RequestParam(required = false) String keyword,
                                         @RequestParam(required = false) Integer level,
                                         @RequestParam(required = false) Long parentId,
                                         @RequestParam(required = false) Integer status) {
        Page<City> cityList = cityService.getCityList(pageNum, pageSize, keyword, level, parentId, status);
        return Result.success("查询成功", cityList);
    }

    @Operation(summary = "获取子城市列表")
    @GetMapping("/children/{parentId}")
    public Result<List<City>> getChildCities(@PathVariable Long parentId) {
        List<City> cities = cityService.getChildCities(parentId);
        return Result.success("查询成功", cities);
    }

    @Operation(summary = "获取热门城市")
    @GetMapping("/hot")
    public Result<List<City>> getHotCities() {
        List<City> cities = cityService.getHotCities();
        return Result.success("查询成功", cities);
    }

    @Operation(summary = "获取已开通服务的城市")
    @GetMapping("/service-enabled")
    public Result<List<City>> getServiceEnabledCities() {
        List<City> cities = cityService.getServiceEnabledCities();
        return Result.success("查询成功", cities);
    }

    @Operation(summary = "获取城市树形结构")
    @GetMapping("/tree")
    public Result<List<City>> getCityTree() {
        List<City> cityTree = cityService.getCityTree();
        return Result.success("查询成功", cityTree);
    }

    @Operation(summary = "添加城市")
    @PostMapping("/add")
    public Result<Boolean> addCity(@Valid @RequestBody City city) {
        boolean result = cityService.addCity(city);
        return Result.success("添加成功", result);
    }

    @Operation(summary = "更新城市")
    @PutMapping("/update")
    public Result<Boolean> updateCity(@Valid @RequestBody City city) {
        boolean result = cityService.updateCity(city);
        return Result.success("更新成功", result);
    }

    @Operation(summary = "删除城市")
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> deleteCity(@PathVariable Long id) {
        boolean result = cityService.deleteCity(id);
        return Result.success("删除成功", result);
    }

    @Operation(summary = "更新城市状态")
    @PutMapping("/status")
    public Result<Boolean> updateCityStatus(@RequestParam Long id, @RequestParam Integer status) {
        boolean result = cityService.updateCityStatus(id, status);
        return Result.success("状态更新成功", result);
    }

    @Operation(summary = "更新服务状态")
    @PutMapping("/service-status")
    public Result<Boolean> updateServiceStatus(@RequestParam Long id, @RequestParam Integer serviceEnabled) {
        boolean result = cityService.updateServiceStatus(id, serviceEnabled);
        return Result.success("服务状态更新成功", result);
    }

    @Operation(summary = "设置热门城市")
    @PutMapping("/hot-status")
    public Result<Boolean> updateHotStatus(@RequestParam Long id, @RequestParam Integer isHot) {
        boolean result = cityService.updateHotStatus(id, isHot);
        return Result.success("热门状态更新成功", result);
    }

    @Operation(summary = "根据城市编码查询城市")
    @GetMapping("/code/{cityCode}")
    public Result<City> getByCityCode(@PathVariable String cityCode) {
        City city = cityService.getByCityCode(cityCode);
        return Result.success("查询成功", city);
    }
}

# CTY-Order 订单服务

## 📋 模块概述
订单服务是平台的业务核心，负责互助需求发布、订单匹配、状态管理等完整的业务流程。

## 🎯 核心功能

### 1. 互助需求管理
- **需求发布**: 用户发布各类互助需求
- **需求分类**: 生活服务、技能互助、紧急求助、物品借用
- **需求编辑**: 修改需求信息和状态
- **需求搜索**: 多维度搜索和筛选

### 2. 智能匹配系统
- **地理匹配**: 基于位置的就近匹配
- **技能匹配**: 根据技能标签精准匹配
- **时间匹配**: 考虑双方时间安排
- **信用匹配**: 优先推荐高信用用户

### 3. 订单生命周期
- **订单创建**: 需求方和帮助方达成意向
- **订单支付**: 资金托管保障
- **服务执行**: 实时状态跟踪
- **订单完成**: 确认完成和资金结算
- **订单评价**: 双向评价机制

### 4. 订单状态管理
```
待接单 → 已接单 → 进行中 → 待确认 → 已完成
   ↓        ↓        ↓        ↓
 已过期   已取消   已取消   已取消
```

## 📊 数据模型

### 互助需求
```java
public class HelpRequest extends BaseEntity {
    private Long userId;              // 发布用户ID
    private Long categoryId;          // 分类ID
    private String title;             // 需求标题
    private String description;       // 需求描述
    private Integer urgencyLevel;     // 紧急程度
    private Integer rewardType;       // 报酬类型
    private BigDecimal rewardAmount;  // 报酬金额
    private String address;           // 地址
    private BigDecimal longitude;     // 经度
    private BigDecimal latitude;      // 纬度
    private Integer status;           // 状态
    // ... 其他字段
}
```

### 订单信息
```java
public class OrderInfo extends BaseEntity {
    private String orderNo;           // 订单号
    private Long requestId;           // 需求ID
    private Long requesterId;         // 需求方ID
    private Long helperId;            // 帮助方ID
    private BigDecimal amount;        // 订单金额
    private Integer status;           // 订单状态
    private LocalDateTime payTime;    // 支付时间
    private LocalDateTime finishTime; // 完成时间
    // ... 其他字段
}
```

## 🔧 核心接口

### 1. 发布需求
```http
POST /order/request/publish
Content-Type: application/json

{
    "categoryId": 1,
    "title": "帮忙代买生活用品",
    "description": "需要代买一些日用品",
    "urgencyLevel": 1,
    "rewardType": 1,
    "rewardAmount": 20.00,
    "address": "北京市朝阳区xxx",
    "longitude": 116.123456,
    "latitude": 39.123456
}
```

### 2. 搜索需求
```http
GET /order/request/search?keyword=代买&city=北京&categoryId=1&page=1&size=10
```

### 3. 接单
```http
POST /order/accept
Content-Type: application/json

{
    "requestId": 123,
    "helperId": 456,
    "message": "我可以帮您代买"
}
```

### 4. 订单状态更新
```http
PUT /order/{orderId}/status
Content-Type: application/json

{
    "status": 3,
    "remark": "服务已开始"
}
```

## 🎯 业务流程

### 需求发布流程
1. 用户填写需求信息
2. 选择需求分类
3. 设置报酬和时间
4. 提交审核
5. 发布成功

### 订单匹配流程
1. 系统智能推荐
2. 用户主动搜索
3. 查看需求详情
4. 发起接单申请
5. 需求方确认

### 订单执行流程
1. 订单创建
2. 支付确认
3. 服务开始
4. 过程跟踪
5. 服务完成
6. 确认结算
7. 双向评价

## 🔍 匹配算法

### 地理位置匹配
- 计算直线距离
- 考虑交通便利性
- 设置距离阈值

### 技能匹配度
- 技能标签匹配
- 技能等级评估
- 历史服务评价

### 综合评分
```
匹配分数 = 地理位置分数 × 0.4 + 技能匹配分数 × 0.3 + 信用分数 × 0.2 + 时间匹配分数 × 0.1
```

## 📈 统计分析
- 需求发布量
- 订单成交率
- 平均响应时间
- 用户满意度
- 热门需求分类

## 🛡️ 风险控制
- 恶意需求检测
- 虚假订单识别
- 纠纷处理机制
- 黑名单管理

## 🚀 技术特性
- **端口**: 8083
- **数据库**: MySQL (city_help_platform)
- **缓存**: Redis (database: 3)
- **消息队列**: RabbitMQ
- **分布式事务**: Seata支持
- **搜索引擎**: Elasticsearch (可选)

# CTY-Payment 支付服务

## 📋 模块概述
支付服务负责处理平台所有的资金流转，包括支付、结算、钱包管理等金融相关功能。

## 🎯 核心功能

### 1. 多元化支付方式
- **余额支付**: 用户钱包余额支付
- **微信支付**: 集成微信支付API
- **支付宝支付**: 集成支付宝支付API
- **银行卡支付**: 快捷支付和网银支付

### 2. 钱包管理系统
- **余额管理**: 实时余额查询和变动
- **充值功能**: 多种方式充值到钱包
- **提现功能**: 余额提现到银行卡
- **冻结机制**: 担保交易资金冻结

### 3. 担保交易
- **资金托管**: 支付时资金先冻结
- **自动结算**: 服务完成后自动释放资金
- **纠纷处理**: 争议期间资金保护
- **退款机制**: 取消订单自动退款

### 4. 资金流水管理
- **交易记录**: 详细的资金变动记录
- **对账功能**: 与第三方支付平台对账
- **财务报表**: 收入支出统计分析
- **风控监控**: 异常交易检测

## 💰 支付流程

### 标准支付流程
```
1. 创建支付订单
2. 选择支付方式
3. 调用支付接口
4. 等待支付结果
5. 更新订单状态
6. 发送支付通知
```

### 担保交易流程
```
1. 用户下单支付
2. 资金冻结到平台
3. 服务方开始服务
4. 服务完成确认
5. 资金释放给服务方
6. 交易完成
```

## 📊 数据模型

### 支付记录
```java
public class PaymentRecord extends BaseEntity {
    private String paymentNo;        // 支付流水号
    private Long orderId;            // 订单ID
    private Long userId;             // 用户ID
    private BigDecimal amount;       // 支付金额
    private Integer payType;         // 支付方式
    private Integer status;          // 支付状态
    private String thirdPartyNo;     // 第三方流水号
    private LocalDateTime payTime;   // 支付时间
    // ... 其他字段
}
```

### 用户钱包
```java
public class UserWallet extends BaseEntity {
    private Long userId;             // 用户ID
    private BigDecimal balance;      // 余额
    private BigDecimal frozenAmount; // 冻结金额
    private BigDecimal totalIncome;  // 总收入
    private BigDecimal totalExpense; // 总支出
    private Integer points;          // 积分
    // ... 其他字段
}
```

### 钱包流水
```java
public class WalletRecord extends BaseEntity {
    private Long userId;             // 用户ID
    private Integer type;            // 类型(收入/支出/冻结/解冻)
    private BigDecimal amount;       // 金额
    private BigDecimal balanceBefore;// 变动前余额
    private BigDecimal balanceAfter; // 变动后余额
    private Integer businessType;    // 业务类型
    private Long businessId;         // 业务ID
    private String description;      // 描述
    // ... 其他字段
}
```

## 🔧 核心接口

### 1. 创建支付订单
```http
POST /payment/create
Content-Type: application/json

{
    "orderId": 123,
    "amount": 50.00,
    "payType": 2,
    "description": "互助服务费用"
}
```

### 2. 查询支付状态
```http
GET /payment/status/{paymentNo}
```

### 3. 钱包充值
```http
POST /payment/wallet/recharge
Content-Type: application/json

{
    "amount": 100.00,
    "payType": 2
}
```

### 4. 钱包提现
```http
POST /payment/wallet/withdraw
Content-Type: application/json

{
    "amount": 50.00,
    "bankCard": "622202**********",
    "bankName": "中国银行"
}
```

### 5. 查询钱包余额
```http
GET /payment/wallet/balance/{userId}
```

## 💳 支付方式配置

### 微信支付
```yaml
wechat:
  pay:
    app-id: wx**********
    mch-id: **********
    api-key: your-api-key
    notify-url: http://domain.com/payment/notify/wechat
```

### 支付宝支付
```yaml
alipay:
  app-id: 202100**********
  private-key: your-private-key
  public-key: alipay-public-key
  notify-url: http://domain.com/payment/notify/alipay
```

## 🛡️ 安全机制

### 1. 数据安全
- 敏感信息加密存储
- 支付密码独立设置
- 交易数据签名验证

### 2. 风控系统
- 异常交易检测
- 频繁操作限制
- 大额交易审核

### 3. 对账机制
- 定时对账任务
- 差异处理流程
- 资金安全保障

## 📈 财务统计
- 交易总额统计
- 手续费收入
- 退款率分析
- 支付方式偏好
- 资金流向分析

## 🔄 异常处理

### 支付失败处理
1. 记录失败原因
2. 释放冻结资金
3. 通知用户重试
4. 更新订单状态

### 网络异常处理
1. 重试机制
2. 超时处理
3. 状态同步
4. 人工介入

## 🚀 技术特性
- **端口**: 8084
- **数据库**: MySQL (city_help_platform)
- **缓存**: Redis (database: 4)
- **消息队列**: RabbitMQ
- **分布式事务**: Seata支持
- **定时任务**: 对账和结算任务

package com.cty.admin.controller;

import com.cty.common.result.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 测试控制器 - 用于调试路径问题
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @GetMapping("/path")
    public Result<String> testPath(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String contextPath = request.getContextPath();
        String servletPath = request.getServletPath();
        
        String info = String.format("RequestURI: %s, ContextPath: %s, ServletPath: %s", 
                                  requestURI, contextPath, servletPath);
        
        System.out.println("路径测试: " + info);
        return Result.success("路径测试成功", info);
    }
    
    @GetMapping("/public")
    public Result<String> publicTest() {
        return Result.success("公开接口测试成功");
    }
}

# 🛣️ API路径映射说明

## 📋 网关路由配置

### 管理后台路由
```yaml
- id: cty-admin
  uri: lb://cty-admin
  predicates:
    - Path=/api/admin/**
  filters:
    - StripPrefix=2  # 移除前两段路径
```

## 🔄 路径转换过程

### 登录接口示例
```
前端请求: POST /api/admin/user/login
    ↓
网关接收: POST /api/admin/user/login
    ↓
路由匹配: /api/admin/** → cty-admin服务
    ↓
StripPrefix=2: 移除 /api/admin
    ↓
转发到微服务: POST /user/login
    ↓
控制器处理: @RequestMapping("/user") + @PostMapping("/login")
```

## 📊 完整路径映射表

| 前端请求路径 | 网关处理后 | 微服务控制器 | 说明 |
|-------------|-----------|-------------|------|
| `/api/admin/user/login` | `/user/login` | `@RequestMapping("/user")` + `@PostMapping("/login")` | 管理员登录 |
| `/api/admin/user/logout` | `/user/logout` | `@RequestMapping("/user")` + `@PostMapping("/logout")` | 管理员登出 |
| `/api/admin/user/info` | `/user/info` | `@RequestMapping("/user")` + `@GetMapping("/info")` | 获取管理员信息 |
| `/api/admin/user/list` | `/user/list` | `@RequestMapping("/user")` + `@GetMapping("/list")` | 获取用户列表 |
| `/api/admin/user/audit` | `/user/audit` | `@RequestMapping("/user")` + `@PostMapping("/audit")` | 用户审核 |
| `/api/admin/user/freeze` | `/user/freeze` | `@RequestMapping("/user")` + `@PostMapping("/freeze")` | 冻结用户 |
| `/api/admin/user/orders` | `/user/orders` | `@RequestMapping("/user")` + `@GetMapping("/orders")` | 获取订单列表 |
| `/api/admin/user/statistics` | `/user/statistics` | `@RequestMapping("/user")` + `@GetMapping("/statistics")` | 获取统计数据 |
| `/api/admin/test/path` | `/test/path` | `@RequestMapping("/test")` + `@GetMapping("/path")` | 路径测试 |

## 🔧 控制器路径修改

### 修改前（错误）
```java
@RestController
@RequestMapping("/admin/user")  // 这会导致404
public class AdminUserController {
    @PostMapping("/login")  // 实际路径: /admin/user/login
    // 但网关转发的是: /user/login
}
```

### 修改后（正确）
```java
@RestController
@RequestMapping("/user")  // 匹配网关转发的路径
public class AdminUserController {
    @PostMapping("/login")  // 实际路径: /user/login
    // 网关转发的也是: /user/login ✅
}
```

## 🔐 Security配置适配

```java
.authorizeRequests(authz -> authz
    // 网关转发后的路径
    .antMatchers("/user/login").permitAll()
    .antMatchers("/user/logout").permitAll()
    
    // 兼容直接访问微服务的路径
    .antMatchers("/admin/user/login").permitAll()
    .antMatchers("/admin/user/logout").permitAll()
    
    // 其他配置...
)
```

## 🚀 测试验证

### 1. 通过网关访问
```bash
# 登录接口
curl -X POST "http://localhost:8080/api/admin/user/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 路径测试接口
curl "http://localhost:8080/api/admin/test/path"
```

### 2. 直接访问微服务
```bash
# 登录接口（需要使用修改前的路径）
curl -X POST "http://localhost:8088/admin/user/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 路径测试接口
curl "http://localhost:8088/test/path"
```

## ⚠️ 注意事项

### 1. 路径一致性
- 确保控制器路径与网关StripPrefix后的路径匹配
- Security配置要同时支持两种访问方式

### 2. 前端调用
- 前端始终使用完整路径（包含/api/admin前缀）
- 网关负责路径转换和服务路由

### 3. 文档更新
- Swagger文档中显示的是微服务实际路径
- 通过网关访问时需要加上前缀

## 🔍 调试技巧

### 1. 查看网关日志
```
Gateway处理请求: POST /api/admin/user/login
```

### 2. 查看微服务日志
```
路径测试: RequestURI: /user/login, ContextPath: , ServletPath: /user/login
```

### 3. 使用测试接口
访问 `/api/admin/test/path` 查看实际接收到的路径信息

## 📝 总结

通过修改控制器的`@RequestMapping`路径，使其与网关StripPrefix后的路径匹配，解决了404问题。这种设计：

✅ **优点**:
- 网关统一路由管理
- 微服务路径简洁
- 支持负载均衡

⚠️ **注意**:
- 需要保持路径一致性
- Security配置需要适配
- 文档需要说明路径映射关系

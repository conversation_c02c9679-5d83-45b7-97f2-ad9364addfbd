# CTY-User 用户服务

## 📋 模块概述
用户服务是平台的核心模块，负责用户账户管理、身份认证、个人信息维护等功能。

## 🎯 核心功能

### 1. 用户账户管理
- **用户注册**: 手机号/邮箱注册，密码加密存储
- **用户登录**: 多种登录方式，登录状态管理
- **密码管理**: 密码修改、找回功能
- **账户安全**: 登录日志、异常检测

### 2. 个人信息管理
- **基础信息**: 昵称、头像、性别、生日等
- **联系方式**: 手机号、邮箱管理
- **地址信息**: 省市区、详细地址、经纬度
- **个人简介**: 自我介绍、兴趣爱好

### 3. 实名认证
- **身份验证**: 身份证号码验证
- **证件上传**: 身份证正反面照片
- **人脸识别**: 活体检测和人脸比对
- **审核流程**: 自动+人工审核机制

### 4. 技能管理
- **技能标签**: 用户擅长的技能分类
- **技能等级**: 初级、中级、高级、专家
- **技能认证**: 相关证书上传验证
- **技能评价**: 基于服务历史的技能评分

### 5. 信用体系
- **信用评分**: 基于行为的动态评分
- **信用记录**: 详细的信用变更历史
- **信用等级**: 不同等级对应不同权益
- **信用恢复**: 信用修复机制

## 📊 数据模型

### 用户基础信息
```java
public class User extends BaseEntity {
    private String username;      // 用户名
    private String nickname;      // 昵称
    private String phone;         // 手机号
    private String email;         // 邮箱
    private String avatar;        // 头像URL
    private Integer gender;       // 性别
    private LocalDate birthday;   // 生日
    private Integer creditScore;  // 信用分
    private Integer status;       // 账户状态
    // ... 其他字段
}
```

### 用户技能
```java
public class UserSkill {
    private Long userId;          // 用户ID
    private String skillName;     // 技能名称
    private Integer skillLevel;   // 技能等级
    private String description;   // 技能描述
    private String certificate;   // 技能证书
}
```

## 🔧 核心接口

### 1. 用户注册
```http
POST /user/register
Content-Type: application/json

{
    "username": "user123",
    "phone": "13800138000",
    "password": "password123",
    "nickname": "小明"
}
```

### 2. 用户登录
```http
POST /user/login
Content-Type: application/x-www-form-urlencoded

username=user123&password=password123
```

### 3. 获取用户信息
```http
GET /user/{id}
Authorization: Bearer {token}
```

### 4. 实名认证
```http
POST /user/auth
Content-Type: application/x-www-form-urlencoded

userId=123&realName=张三&idCard=110101199001011234&idCardFront=url1&idCardBack=url2
```

## 🛡️ 安全机制

### 1. 密码安全
- BCrypt加密存储
- 密码强度校验
- 防暴力破解

### 2. 数据保护
- 敏感信息脱敏
- 数据传输加密
- 访问权限控制

### 3. 防刷机制
- 注册频率限制
- 登录尝试限制
- 验证码保护

## 📈 业务流程

### 用户注册流程
1. 输入基础信息
2. 手机号验证
3. 密码设置
4. 账户创建
5. 发送欢迎消息

### 实名认证流程
1. 提交身份信息
2. 证件照片上传
3. 系统初步验证
4. 人工审核
5. 认证结果通知

## 🔍 监控指标
- 注册用户数
- 活跃用户数
- 实名认证率
- 信用分布情况
- 登录成功率

## 🚀 技术特性
- **端口**: 8082
- **数据库**: MySQL (city_help_platform)
- **缓存**: Redis (database: 2)
- **消息队列**: RabbitMQ
- **分布式事务**: Seata支持

{"version": 3, "names": ["_inherit", "require", "inheritInnerComments", "child", "parent", "inherit"], "sources": ["../../src/comments/inheritInnerComments.ts"], "sourcesContent": ["import inherit from \"../utils/inherit.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function inheritInnerComments(\n  child: t.Node,\n  parent: t.Node,\n): void {\n  inherit(\"innerComments\", child, parent);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAGe,SAASC,oBAAoBA,CAC1CC,KAAa,EACbC,MAAc,EACR;EACN,IAAAC,gBAAO,EAAC,eAAe,EAAEF,KAAK,EAAEC,MAAM,CAAC;AACzC", "ignoreList": []}
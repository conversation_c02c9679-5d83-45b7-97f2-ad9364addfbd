package com.cty.admin.filter;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;

/**
 * JWT认证过滤器
 * 用于验证从网关传递过来的JWT Token
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  Filter<PERSON>hain filterChain) throws ServletException, IOException {
        
        // 获取Authorization头
        String authHeader = request.getHeader("Authorization");
        
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            
            // 这里应该验证JWT Token的有效性
            // 为了简化，暂时只检查token是否存在
            if (StringUtils.hasText(token)) {
                // 从token中解析用户信息（这里简化处理）
                String username = parseUsernameFromToken(token);
                
                if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                    // 创建认证对象
                    UsernamePasswordAuthenticationToken authToken = 
                        new UsernamePasswordAuthenticationToken(username, null, new ArrayList<>());
                    
                    // 设置到Security上下文
                    SecurityContextHolder.getContext().setAuthentication(authToken);
                }
            }
        }
        
        filterChain.doFilter(request, response);
    }
    
    /**
     * 从Token中解析用户名
     * 实际项目中应该使用JWT库来解析
     */
    private String parseUsernameFromToken(String token) {
        // 简化处理：如果token以admin_token_开头，就认为是admin用户
        if (token.startsWith("admin_token_")) {
            return "admin";
        }
        return null;
    }
}

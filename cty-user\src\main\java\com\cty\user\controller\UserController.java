package com.cty.user.controller;

import com.cty.common.result.Result;
import com.cty.user.entity.User;
import com.cty.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;

/**
 * 用户控制器
 */
@Tag(name = "用户管理", description = "用户相关接口")
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    @Operation(summary = "用户注册")
    @PostMapping("/register")
    public Result<User> register(@Valid @RequestBody User user) {
        User result = userService.register(user);
        return Result.success("注册成功", result);
    }
    
    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<User> login(@RequestParam String username, @RequestParam String password) {
        User user = userService.login(username, password);
        return Result.success("登录成功", user);
    }
    
    @Operation(summary = "获取用户信息")
    @GetMapping("/{id}")
    public Result<User> getUserById(@PathVariable Long id) {
        User user = userService.getById(id);
        return Result.success(user);
    }
    
    @Operation(summary = "根据用户名获取用户")
    @GetMapping("/username/{username}")
    public Result<User> getUserByUsername(@PathVariable String username) {
        User user = userService.getByUsername(username);
        return Result.success(user);
    }
    
    @Operation(summary = "根据手机号获取用户")
    @GetMapping("/phone/{phone}")
    public Result<User> getUserByPhone(@PathVariable String phone) {
        User user = userService.getByPhone(phone);
        return Result.success(user);
    }
    
    @Operation(summary = "更新用户信息")
    @PutMapping("/update")
    public Result<Boolean> updateUser(@Valid @RequestBody User user) {
        boolean result = userService.updateUserInfo(user);
        return Result.success("更新成功", result);
    }
    
    @Operation(summary = "实名认证")
    @PostMapping("/auth")
    public Result<Boolean> realNameAuth(@RequestParam Long userId,
                                       @RequestParam String realName,
                                       @RequestParam String idCard,
                                       @RequestParam String idCardFront,
                                       @RequestParam String idCardBack) {
        boolean result = userService.realNameAuth(userId, realName, idCard, idCardFront, idCardBack);
        return Result.success("提交认证成功", result);
    }
    
    @Operation(summary = "更新用户位置")
    @PostMapping("/location")
    public Result<Boolean> updateLocation(@RequestParam Long userId,
                                         @RequestParam String province,
                                         @RequestParam String city,
                                         @RequestParam String district,
                                         @RequestParam String address,
                                         @RequestParam BigDecimal longitude,
                                         @RequestParam BigDecimal latitude) {
        boolean result = userService.updateLocation(userId, province, city, district, address, longitude, latitude);
        return Result.success("位置更新成功", result);
    }
}

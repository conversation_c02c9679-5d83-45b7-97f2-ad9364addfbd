<template>
  <PageContainer>
    <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon user">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ userStats.total }}</h3>
              <p>总用户数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon order">
              <el-icon><List /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ orderStats.total }}</h3>
              <p>总订单数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon money">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stats-info">
              <h3>¥{{ orderStats.amount }}</h3>
              <p>交易总额</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ (orderStats.completionRate * 100).toFixed(1) }}%</h3>
              <p>完成率</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户增长趋势</span>
          </template>
          <div ref="userChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>订单统计</span>
          </template>
          <div ref="orderChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新数据 -->
    <el-row :gutter="20" class="recent-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>最新用户</span>
          </template>
          <el-table :data="recentUsers" style="width: 100%">
            <el-table-column prop="username" label="用户名" />
            <el-table-column prop="nickname" label="昵称" />
            <el-table-column prop="createTime" label="注册时间" />
            <el-table-column label="状态">
              <template #default="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                  {{ scope.row.status === 1 ? '正常' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>最新订单</span>
          </template>
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="orderNo" label="订单号" />
            <el-table-column prop="amount" label="金额" />
            <el-table-column prop="createTime" label="创建时间" />
            <el-table-column label="状态">
              <template #default="scope">
                <el-tag :type="getOrderStatusType(scope.row.status)">
                  {{ getOrderStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    </div>
  </PageContainer>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getStatistics } from '@/api/user'
import PageContainer from '@/components/PageContainer.vue'

const userChartRef = ref()
const orderChartRef = ref()

const userStats = ref({
  total: 0,
  new: 0,
  active: 0,
  verified: 0
})

const orderStats = ref({
  total: 0,
  completed: 0,
  amount: 0,
  completionRate: 0
})

const recentUsers = ref([
  { username: 'user001', nickname: '张三', createTime: '2023-12-01 10:00', status: 1 },
  { username: 'user002', nickname: '李四', createTime: '2023-12-01 11:00', status: 1 },
  { username: 'user003', nickname: '王五', createTime: '2023-12-01 12:00', status: 0 }
])

const recentOrders = ref([
  { orderNo: 'ORD202312010001', amount: '¥20.00', createTime: '2023-12-01 10:00', status: 5 },
  { orderNo: 'ORD202312010002', amount: '¥35.00', createTime: '2023-12-01 11:00', status: 3 },
  { orderNo: 'ORD202312010003', amount: '¥15.00', createTime: '2023-12-01 12:00', status: 1 }
])

// 获取订单状态类型
const getOrderStatusType = (status) => {
  const statusMap = {
    1: 'warning',  // 待支付
    2: 'info',     // 已支付
    3: 'primary',  // 进行中
    4: 'warning',  // 待确认
    5: 'success',  // 已完成
    6: 'danger'    // 已取消
  }
  return statusMap[status] || 'info'
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
  const statusMap = {
    1: '待支付',
    2: '已支付',
    3: '进行中',
    4: '待确认',
    5: '已完成',
    6: '已取消'
  }
  return statusMap[status] || '未知'
}

// 初始化用户增长图表
const initUserChart = () => {
  const chart = echarts.init(userChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [120, 200, 150, 80, 70, 110, 130, 180, 220, 280, 350, 400],
      type: 'line',
      smooth: true,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
          { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
        ])
      }
    }]
  }
  chart.setOption(option)
}

// 初始化订单统计图表
const initOrderChart = () => {
  const chart = echarts.init(orderChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      type: 'pie',
      radius: '50%',
      data: [
        { value: 450, name: '已完成' },
        { value: 30, name: '进行中' },
        { value: 15, name: '待支付' },
        { value: 5, name: '已取消' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  chart.setOption(option)
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const userResult = await getStatistics({ type: 'user', period: 'month' })
    if (userResult.code === 200) {
      userStats.value = userResult.data
    }
    
    const orderResult = await getStatistics({ type: 'order', period: 'month' })
    if (orderResult.code === 200) {
      orderStats.value = orderResult.data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

onMounted(async () => {
  await fetchStatistics()
  await nextTick()
  initUserChart()
  initOrderChart()
})
</script>

<style lang="scss" scoped>
.dashboard {
  min-height: 100%;

  .stats-row {
    margin-bottom: 20px;
  }
  
  .stats-card {
    .stats-content {
      display: flex;
      align-items: center;
      
      .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        
        .el-icon {
          font-size: 24px;
          color: #fff;
        }
        
        &.user {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        &.order {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        &.money {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        &.rate {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }
      
      .stats-info {
        h3 {
          margin: 0 0 5px 0;
          font-size: 24px;
          font-weight: bold;
          color: #333;
        }
        
        p {
          margin: 0;
          color: #666;
          font-size: 14px;
        }
      }
    }
  }
  
  .charts-row {
    margin-bottom: 20px;
  }
  
  .recent-row {
    margin-bottom: 20px;
  }
}
</style>

server:
  port: 8080

spring:
  application:
    name: cty-gateway
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
      config:
        server-addr: localhost:8848
        namespace: dev
        file-extension: yml
        group: DEFAULT_GROUP
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 用户服务路由
        - id: cty-user
          uri: lb://cty-user
          predicates:
            - Path=/api/user/**
          filters:
            - StripPrefix=2
        # 订单服务路由
        - id: cty-order
          uri: lb://cty-order
          predicates:
            - Path=/api/order/**
          filters:
            - StripPrefix=2
        # 支付服务路由
        - id: cty-payment
          uri: lb://cty-payment
          predicates:
            - Path=/api/payment/**
          filters:
            - StripPrefix=2
        # 消息服务路由
        - id: cty-message
          uri: lb://cty-message
          predicates:
            - Path=/api/message/**
          filters:
            - StripPrefix=2
        # 位置服务路由
        - id: cty-location
          uri: lb://cty-location
          predicates:
            - Path=/api/location/**
          filters:
            - StripPrefix=2
        # 文件服务路由
        - id: cty-file
          uri: lb://cty-file
          predicates:
            - Path=/api/file/**
          filters:
            - StripPrefix=2
        # 管理后台服务路由
        - id: cty-admin
          uri: lb://cty-admin
          predicates:
            - Path=/api/admin/**
          filters:
            - StripPrefix=2

# Knife4j配置
knife4j:
  gateway:
    enabled: true
    strategy: discover
    discover:
      enabled: true
      version: openapi3
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true

  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

logging:
  level:
    com.cty.gateway: debug
    org.springframework.cloud.gateway: debug

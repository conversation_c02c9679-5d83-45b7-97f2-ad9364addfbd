server:
  port: 8081

spring:
  application:
    name: cty-auth
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
      config:
        server-addr: localhost:8848
        namespace: dev
        file-extension: yml
        group: DEFAULT_GROUP

  redis:
    host: localhost
    port: 6379
    password: 
    database: 1
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# JWT配置
jwt:
  secret: cty-help-platform-secret-key-2023
  expiration: 86400 # 24小时
  refresh-expiration: 604800 # 7天

logging:
  level:
    com.cty.auth: debug

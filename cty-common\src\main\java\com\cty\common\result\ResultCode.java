package com.cty.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    
    // 用户相关状态码
    USER_NOT_EXIST(1001, "用户不存在"),
    USER_ALREADY_EXIST(1002, "用户已存在"),
    PASSWORD_ERROR(1003, "密码错误"),
    USER_DISABLED(1004, "用户已被禁用"),
    USER_NOT_LOGIN(1005, "用户未登录"),
    TOKEN_EXPIRED(1006, "Token已过期"),
    TOKEN_INVALID(1007, "Token无效"),
    
    // 订单相关状态码
    ORDER_NOT_EXIST(2001, "订单不存在"),
    ORDER_STATUS_ERROR(2002, "订单状态错误"),
    ORDER_CANNOT_CANCEL(2003, "订单无法取消"),
    ORDER_ALREADY_PAID(2004, "订单已支付"),
    
    // 支付相关状态码
    PAYMENT_ERROR(3001, "支付失败"),
    PAYMENT_TIMEOUT(3002, "支付超时"),
    BALANCE_NOT_ENOUGH(3003, "余额不足"),
    
    // 消息相关状态码
    MESSAGE_SEND_FAILED(4001, "消息发送失败"),
    
    // 文件相关状态码
    FILE_UPLOAD_FAILED(5001, "文件上传失败"),
    FILE_TYPE_ERROR(5002, "文件类型错误"),
    FILE_SIZE_EXCEEDED(5003, "文件大小超限"),
    
    // 位置相关状态码
    LOCATION_ERROR(6001, "位置信息错误"),
    DISTANCE_TOO_FAR(6002, "距离过远");
    
    private final Integer code;
    private final String message;
}

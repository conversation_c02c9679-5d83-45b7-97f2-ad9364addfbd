@echo off
echo ========================================
echo 同城互助平台微服务启动脚本
echo ========================================

echo.
echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo Java环境未配置，请先安装JDK 8+
    pause
    exit /b 1
)

echo.
echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo Maven环境未配置，请先安装Maven 3.6+
    pause
    exit /b 1
)

echo.
echo ========================================
echo 开始启动微服务...
echo ========================================

echo.
echo [1/9] 启动网关服务 (端口: 8080)...
start "Gateway Service" cmd /k "cd /d %~dp0.. && mvn spring-boot:run -pl cty-gateway"
timeout /t 10

echo.
echo [2/9] 启动认证服务 (端口: 8081)...
start "Auth Service" cmd /k "cd /d %~dp0.. && mvn spring-boot:run -pl cty-auth"
timeout /t 5

echo.
echo [3/9] 启动用户服务 (端口: 8082)...
start "User Service" cmd /k "cd /d %~dp0.. && mvn spring-boot:run -pl cty-user"
timeout /t 5

echo.
echo [4/9] 启动订单服务 (端口: 8083)...
start "Order Service" cmd /k "cd /d %~dp0.. && mvn spring-boot:run -pl cty-order"
timeout /t 5

echo.
echo [5/9] 启动支付服务 (端口: 8084)...
start "Payment Service" cmd /k "cd /d %~dp0.. && mvn spring-boot:run -pl cty-payment"
timeout /t 5

echo.
echo [6/9] 启动消息服务 (端口: 8085)...
start "Message Service" cmd /k "cd /d %~dp0.. && mvn spring-boot:run -pl cty-message"
timeout /t 5

echo.
echo [7/9] 启动位置服务 (端口: 8086)...
start "Location Service" cmd /k "cd /d %~dp0.. && mvn spring-boot:run -pl cty-location"
timeout /t 5

echo.
echo [8/9] 启动文件服务 (端口: 8087)...
start "File Service" cmd /k "cd /d %~dp0.. && mvn spring-boot:run -pl cty-file"
timeout /t 5

echo.
echo [9/9] 启动管理后台服务 (端口: 8088)...
start "Admin Service" cmd /k "cd /d %~dp0.. && mvn spring-boot:run -pl cty-admin"

echo.
echo ========================================
echo 所有微服务启动完成！
echo ========================================
echo.
echo 服务访问地址：
echo - 网关服务: http://localhost:8080
echo - API文档: http://localhost:8080/doc.html
echo - 认证服务: http://localhost:8081
echo - 用户服务: http://localhost:8082
echo - 订单服务: http://localhost:8083
echo - 支付服务: http://localhost:8084
echo - 消息服务: http://localhost:8085
echo - 位置服务: http://localhost:8086
echo - 文件服务: http://localhost:8087
echo - 管理后台: http://localhost:8088
echo.
echo 请确保以下基础服务已启动：
echo - MySQL (端口: 3306)
echo - Redis (端口: 6379)
echo - RabbitMQ (端口: 5672)
echo - Nacos (端口: 8848)
echo.
pause

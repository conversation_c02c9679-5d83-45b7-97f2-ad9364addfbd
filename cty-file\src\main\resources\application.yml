server:
  port: 8087

spring:
  application:
    name: cty-file
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
      config:
        server-addr: localhost:8848
        namespace: dev
        file-extension: yml
        group: DEFAULT_GROUP

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

  redis:
    host: localhost
    port: 6379
    password: 
    database: 6
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# 文件存储配置
file:
  # 存储类型 local/minio/oss
  storage-type: local
  # 本地存储配置
  local:
    path: /data/files
    url-prefix: http://localhost:8087/files
  # MinIO配置
  minio:
    endpoint: http://localhost:9000
    access-key: minioadmin
    secret-key: minioadmin
    bucket-name: city-help
  # 阿里云OSS配置
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    access-key-id: your-access-key-id
    access-key-secret: your-access-key-secret
    bucket-name: city-help

# 文件类型限制
file-type:
  image:
    allowed-types: jpg,jpeg,png,gif,bmp,webp
    max-size: 5MB
  document:
    allowed-types: pdf,doc,docx,xls,xlsx,ppt,pptx,txt
    max-size: 10MB
  video:
    allowed-types: mp4,avi,mov,wmv,flv
    max-size: 100MB

logging:
  level:
    com.cty.file: debug

package com.cty.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cty.admin.dto.LoginRequest;
import com.cty.admin.entity.AdminUser;
import com.cty.admin.service.AdminUserService;
import com.cty.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 管理员用户控制器
 */
@Tag(name = "管理员管理", description = "管理员用户相关接口")
@RestController
@RequestMapping("/user")  // 修改为 /user，因为网关会StripPrefix
@RequiredArgsConstructor
public class AdminUserController {

    private final AdminUserService adminUserService;

    @Operation(summary = "管理员登录")
    @PostMapping("/login")
    public Result<String> login(@Valid @RequestBody LoginRequest request) {
        String token = adminUserService.login(request.getUsername(), request.getPassword());
        return Result.success("登录成功", token);
    }

    @Operation(summary = "获取管理员信息")
    @GetMapping("/info")
    public Result<AdminUser> getUserInfo(@RequestParam String username) {
        AdminUser admin = adminUserService.getByUsername(username);
        return Result.success(admin);
    }

    @Operation(summary = "管理员登出")
    @PostMapping("/logout")
    public Result<Void> logout() {
        return Result.success("登出成功", null);
    }

    @Operation(summary = "获取用户列表")
    @GetMapping("/list")
    public Result<Page<Object>> getUserList(@RequestParam(defaultValue = "1") Integer pageNum,
                                           @RequestParam(defaultValue = "10") Integer pageSize,
                                           @RequestParam(required = false) String keyword,
                                           @RequestParam(required = false) Integer status) {
        Page<Object> userList = adminUserService.getUserList(pageNum, pageSize, keyword, status);
        return Result.success("查询成功", userList);
    }

    @Operation(summary = "用户实名认证审核")
    @PostMapping("/audit")
    public Result<Boolean> auditUser(@RequestParam Long userId,
                                    @RequestParam Integer status,
                                    @RequestParam(required = false) String remark) {
        boolean result = adminUserService.auditUser(userId, status, remark);
        return Result.success("审核成功", result);
    }

    @Operation(summary = "冻结/解冻用户")
    @PostMapping("/freeze")
    public Result<Boolean> freezeUser(@RequestParam Long userId,
                                     @RequestParam Integer status,
                                     @RequestParam(required = false) String reason) {
        boolean result = adminUserService.freezeUser(userId, status, reason);
        return Result.success("操作成功", result);
    }

    @Operation(summary = "获取订单列表")
    @GetMapping("/orders")
    public Result<Page<Object>> getOrderList(@RequestParam(defaultValue = "1") Integer pageNum,
                                            @RequestParam(defaultValue = "10") Integer pageSize,
                                            @RequestParam(required = false) Integer status,
                                            @RequestParam(required = false) String startTime,
                                            @RequestParam(required = false) String endTime) {
        Page<Object> orderList = adminUserService.getOrderList(pageNum, pageSize, status, startTime, endTime);
        return Result.success("查询成功", orderList);
    }

    @Operation(summary = "获取统计数据")
    @GetMapping("/statistics")
    public Result<Object> getStatistics(@RequestParam(defaultValue = "user") String type,
                                       @RequestParam(defaultValue = "month") String period) {
        Object statistics = adminUserService.getStatistics(type, period);
        return Result.success("查询成功", statistics);
    }
}

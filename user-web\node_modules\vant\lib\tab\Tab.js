var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name2 in all)
    __defProp(target, name2, { get: all[name2], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var stdin_exports = {};
__export(stdin_exports, {
  default: () => stdin_default,
  tabProps: () => tabProps
});
module.exports = __toCommonJS(stdin_exports);
var import_vue = require("vue");
var import_shared = require("@vue/shared");
var import_utils = require("../utils");
var import_Tabs = require("../tabs/Tabs");
var import_use = require("@vant/use");
var import_use_id = require("../composables/use-id");
var import_use_expose = require("../composables/use-expose");
var import_use_route = require("../composables/use-route");
var import_use_tab_status = require("../composables/use-tab-status");
var import_TabTitle = require("./TabTitle");
var import_swipe_item = require("../swipe-item");
const [name, bem] = (0, import_utils.createNamespace)("tab");
const tabProps = (0, import_utils.extend)({}, import_use_route.routeProps, {
  dot: Boolean,
  name: import_utils.numericProp,
  badge: import_utils.numericProp,
  title: String,
  disabled: Boolean,
  titleClass: import_utils.unknownProp,
  titleStyle: [String, Object],
  showZeroBadge: import_utils.truthProp
});
var stdin_default = (0, import_vue.defineComponent)({
  name,
  props: tabProps,
  setup(props, {
    slots
  }) {
    const id = (0, import_use_id.useId)();
    const inited = (0, import_vue.ref)(false);
    const instance = (0, import_vue.getCurrentInstance)();
    const {
      parent,
      index
    } = (0, import_use.useParent)(import_Tabs.TABS_KEY);
    if (!parent) {
      if (process.env.NODE_ENV !== "production") {
        console.error("[Vant] <Tab> must be a child component of <Tabs>.");
      }
      return;
    }
    const getName = () => {
      var _a;
      return (_a = props.name) != null ? _a : index.value;
    };
    const init = () => {
      inited.value = true;
      if (parent.props.lazyRender) {
        (0, import_vue.nextTick)(() => {
          parent.onRendered(getName(), props.title);
        });
      }
    };
    const active = (0, import_vue.computed)(() => {
      const isActive = getName() === parent.currentName.value;
      if (isActive && !inited.value) {
        init();
      }
      return isActive;
    });
    const parsedClass = (0, import_vue.ref)("");
    const parsedStyle = (0, import_vue.ref)("");
    (0, import_vue.watchEffect)(() => {
      const {
        titleClass,
        titleStyle
      } = props;
      parsedClass.value = titleClass ? (0, import_shared.normalizeClass)(titleClass) : "";
      parsedStyle.value = titleStyle && typeof titleStyle !== "string" ? (0, import_shared.stringifyStyle)((0, import_shared.normalizeStyle)(titleStyle)) : titleStyle;
    });
    const renderTitle = (onClickTab) => (0, import_vue.createVNode)(import_TabTitle.TabTitle, (0, import_vue.mergeProps)({
      "key": id,
      "id": `${parent.id}-${index.value}`,
      "ref": parent.setTitleRefs(index.value),
      "style": parsedStyle.value,
      "class": parsedClass.value,
      "isActive": active.value,
      "controls": id,
      "scrollable": parent.scrollable.value,
      "activeColor": parent.props.titleActiveColor,
      "inactiveColor": parent.props.titleInactiveColor,
      "onClick": (event) => onClickTab(instance.proxy, index.value, event)
    }, (0, import_utils.pick)(parent.props, ["type", "color", "shrink"]), (0, import_utils.pick)(props, ["dot", "badge", "title", "disabled", "showZeroBadge"])), {
      title: slots.title
    });
    const hasInactiveClass = (0, import_vue.ref)(!active.value);
    (0, import_vue.watch)(active, (val) => {
      if (val) {
        hasInactiveClass.value = false;
      } else {
        (0, import_use.doubleRaf)(() => {
          hasInactiveClass.value = true;
        });
      }
    });
    (0, import_vue.watch)(() => props.title, () => {
      parent.setLine();
      parent.scrollIntoView();
    });
    (0, import_use_tab_status.useProvideTabStatus)(active);
    (0, import_use_expose.useExpose)({
      id,
      renderTitle
    });
    return () => {
      var _a;
      const label = `${parent.id}-${index.value}`;
      const {
        animated,
        swipeable,
        scrollspy,
        lazyRender
      } = parent.props;
      if (!slots.default && !animated) {
        return;
      }
      const show = scrollspy || active.value;
      if (animated || swipeable) {
        return (0, import_vue.createVNode)(import_swipe_item.SwipeItem, {
          "id": id,
          "role": "tabpanel",
          "class": bem("panel-wrapper", {
            inactive: hasInactiveClass.value
          }),
          "tabindex": active.value ? 0 : -1,
          "aria-hidden": !active.value,
          "aria-labelledby": label,
          "data-allow-mismatch": "attribute"
        }, {
          default: () => {
            var _a2;
            return [(0, import_vue.createVNode)("div", {
              "class": bem("panel")
            }, [(_a2 = slots.default) == null ? void 0 : _a2.call(slots)])];
          }
        });
      }
      const shouldRender = inited.value || scrollspy || !lazyRender;
      const Content = shouldRender ? (_a = slots.default) == null ? void 0 : _a.call(slots) : null;
      return (0, import_vue.withDirectives)((0, import_vue.createVNode)("div", {
        "id": id,
        "role": "tabpanel",
        "class": bem("panel"),
        "tabindex": show ? 0 : -1,
        "aria-labelledby": label,
        "data-allow-mismatch": "attribute"
      }, [Content]), [[import_vue.vShow, show]]);
    };
  }
});

{"version": 3, "file": "client.mjs", "sources": ["overlay.ts", "client.ts"], "sourcesContent": ["import type { ErrorPayload } from 'types/hmrPayload'\n\n// injected by the hmr plugin when served\ndeclare const __BASE__: string\n\nconst base = __BASE__ || '/'\n\n// set :host styles to make playwright detect the element as visible\nconst template = /*html*/ `\n<style>\n:host {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 99999;\n  --monospace: 'SFMono-Regular', <PERSON>solas,\n  'Liberation Mono', Menlo, Courier, monospace;\n  --red: #ff5555;\n  --yellow: #e2aa53;\n  --purple: #cfa4ff;\n  --cyan: #2dd9da;\n  --dim: #c9c9c9;\n\n  --window-background: #181818;\n  --window-color: #d8d8d8;\n}\n\n.backdrop {\n  position: fixed;\n  z-index: 99999;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  overflow-y: scroll;\n  margin: 0;\n  background: rgba(0, 0, 0, 0.66);\n}\n\n.window {\n  font-family: var(--monospace);\n  line-height: 1.5;\n  width: 800px;\n  color: var(--window-color);\n  margin: 30px auto;\n  padding: 25px 40px;\n  position: relative;\n  background: var(--window-background);\n  border-radius: 6px 6px 8px 8px;\n  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);\n  overflow: hidden;\n  border-top: 8px solid var(--red);\n  direction: ltr;\n  text-align: left;\n}\n\npre {\n  font-family: var(--monospace);\n  font-size: 16px;\n  margin-top: 0;\n  margin-bottom: 1em;\n  overflow-x: scroll;\n  scrollbar-width: none;\n}\n\npre::-webkit-scrollbar {\n  display: none;\n}\n\n.message {\n  line-height: 1.3;\n  font-weight: 600;\n  white-space: pre-wrap;\n}\n\n.message-body {\n  color: var(--red);\n}\n\n.plugin {\n  color: var(--purple);\n}\n\n.file {\n  color: var(--cyan);\n  margin-bottom: 0;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.frame {\n  color: var(--yellow);\n}\n\n.stack {\n  font-size: 13px;\n  color: var(--dim);\n}\n\n.tip {\n  font-size: 13px;\n  color: #999;\n  border-top: 1px dotted #999;\n  padding-top: 13px;\n  line-height: 1.8;\n}\n\ncode {\n  font-size: 13px;\n  font-family: var(--monospace);\n  color: var(--yellow);\n}\n\n.file-link {\n  text-decoration: underline;\n  cursor: pointer;\n}\n\nkbd {\n  line-height: 1.5;\n  font-family: ui-monospace, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  font-size: 0.75rem;\n  font-weight: 700;\n  background-color: rgb(38, 40, 44);\n  color: rgb(166, 167, 171);\n  padding: 0.15rem 0.3rem;\n  border-radius: 0.25rem;\n  border-width: 0.0625rem 0.0625rem 0.1875rem;\n  border-style: solid;\n  border-color: rgb(54, 57, 64);\n  border-image: initial;\n}\n</style>\n<div class=\"backdrop\" part=\"backdrop\">\n  <div class=\"window\" part=\"window\">\n    <pre class=\"message\" part=\"message\"><span class=\"plugin\" part=\"plugin\"></span><span class=\"message-body\" part=\"message-body\"></span></pre>\n    <pre class=\"file\" part=\"file\"></pre>\n    <pre class=\"frame\" part=\"frame\"></pre>\n    <pre class=\"stack\" part=\"stack\"></pre>\n    <div class=\"tip\" part=\"tip\">\n      Click outside, press <kbd>Esc</kbd> key, or fix the code to dismiss.<br>\n      You can also disable this overlay by setting\n      <code part=\"config-option-name\">server.hmr.overlay</code> to <code part=\"config-option-value\">false</code> in <code part=\"config-file-name\">vite.config.js.</code>\n    </div>\n  </div>\n</div>\n`\n\nconst fileRE = /(?:[a-zA-Z]:\\\\|\\/).*?:\\d+:\\d+/g\nconst codeframeRE = /^(?:>?\\s+\\d+\\s+\\|.*|\\s+\\|\\s*\\^.*)\\r?\\n/gm\n\n// Allow `ErrorOverlay` to extend `HTMLElement` even in environments where\n// `HTMLElement` was not originally defined.\nconst { HTMLElement = class {} as typeof globalThis.HTMLElement } = globalThis\nexport class ErrorOverlay extends HTMLElement {\n  root: ShadowRoot\n  closeOnEsc: (e: KeyboardEvent) => void\n\n  constructor(err: ErrorPayload['err'], links = true) {\n    super()\n    this.root = this.attachShadow({ mode: 'open' })\n    this.root.innerHTML = template\n\n    codeframeRE.lastIndex = 0\n    const hasFrame = err.frame && codeframeRE.test(err.frame)\n    const message = hasFrame\n      ? err.message.replace(codeframeRE, '')\n      : err.message\n    if (err.plugin) {\n      this.text('.plugin', `[plugin:${err.plugin}] `)\n    }\n    this.text('.message-body', message.trim())\n\n    const [file] = (err.loc?.file || err.id || 'unknown file').split(`?`)\n    if (err.loc) {\n      this.text('.file', `${file}:${err.loc.line}:${err.loc.column}`, links)\n    } else if (err.id) {\n      this.text('.file', file)\n    }\n\n    if (hasFrame) {\n      this.text('.frame', err.frame!.trim())\n    }\n    this.text('.stack', err.stack, links)\n\n    this.root.querySelector('.window')!.addEventListener('click', (e) => {\n      e.stopPropagation()\n    })\n\n    this.addEventListener('click', () => {\n      this.close()\n    })\n\n    this.closeOnEsc = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' || e.code === 'Escape') {\n        this.close()\n      }\n    }\n\n    document.addEventListener('keydown', this.closeOnEsc)\n  }\n\n  text(selector: string, text: string, linkFiles = false): void {\n    const el = this.root.querySelector(selector)!\n    if (!linkFiles) {\n      el.textContent = text\n    } else {\n      let curIndex = 0\n      let match: RegExpExecArray | null\n      fileRE.lastIndex = 0\n      while ((match = fileRE.exec(text))) {\n        const { 0: file, index } = match\n        if (index != null) {\n          const frag = text.slice(curIndex, index)\n          el.appendChild(document.createTextNode(frag))\n          const link = document.createElement('a')\n          link.textContent = file\n          link.className = 'file-link'\n          link.onclick = () => {\n            fetch(`${base}__open-in-editor?file=` + encodeURIComponent(file))\n          }\n          el.appendChild(link)\n          curIndex += frag.length + file.length\n        }\n      }\n    }\n  }\n  close(): void {\n    this.parentNode?.removeChild(this)\n    document.removeEventListener('keydown', this.closeOnEsc)\n  }\n}\n\nexport const overlayId = 'vite-error-overlay'\nconst { customElements } = globalThis // Ensure `customElements` is defined before the next line.\nif (customElements && !customElements.get(overlayId)) {\n  customElements.define(overlayId, ErrorOverlay)\n}\n", "import type { ErrorPayload, HMRPayload, Update } from 'types/hmrPayload'\nimport type { ModuleNamespace, ViteHotContext } from 'types/hot'\nimport type { InferCustomEventPayload } from 'types/customEvent'\nimport { ErrorOverlay, overlayId } from './overlay'\nimport '@vite/env'\n\n// injected by the hmr plugin when served\ndeclare const __BASE__: string\ndeclare const __SERVER_HOST__: string\ndeclare const __HMR_PROTOCOL__: string | null\ndeclare const __HMR_HOSTNAME__: string | null\ndeclare const __HMR_PORT__: number | null\ndeclare const __HMR_DIRECT_TARGET__: string\ndeclare const __HMR_BASE__: string\ndeclare const __HMR_TIMEOUT__: number\ndeclare const __HMR_ENABLE_OVERLAY__: boolean\ndeclare const __WS_TOKEN__: string\n\nconsole.debug('[vite] connecting...')\n\nconst importMetaUrl = new URL(import.meta.url)\n\n// use server configuration, then fallback to inference\nconst serverHost = __SERVER_HOST__\nconst socketProtocol =\n  __HMR_PROTOCOL__ || (importMetaUrl.protocol === 'https:' ? 'wss' : 'ws')\nconst hmrPort = __HMR_PORT__\nconst socketHost = `${__HMR_HOSTNAME__ || importMetaUrl.hostname}:${\n  hmrPort || importMetaUrl.port\n}${__HMR_BASE__}`\nconst directSocketHost = __HMR_DIRECT_TARGET__\nconst base = __BASE__ || '/'\nconst wsToken = __WS_TOKEN__\nconst messageBuffer: string[] = []\n\nlet socket: WebSocket\ntry {\n  let fallback: (() => void) | undefined\n  // only use fallback when port is inferred to prevent confusion\n  if (!hmrPort) {\n    fallback = () => {\n      // fallback to connecting directly to the hmr server\n      // for servers which does not support proxying websocket\n      socket = setupWebSocket(socketProtocol, directSocketHost, () => {\n        const currentScriptHostURL = new URL(import.meta.url)\n        const currentScriptHost =\n          currentScriptHostURL.host +\n          currentScriptHostURL.pathname.replace(/@vite\\/client$/, '')\n        console.error(\n          '[vite] failed to connect to websocket.\\n' +\n            'your current setup:\\n' +\n            `  (browser) ${currentScriptHost} <--[HTTP]--> ${serverHost} (server)\\n` +\n            `  (browser) ${socketHost} <--[WebSocket (failing)]--> ${directSocketHost} (server)\\n` +\n            'Check out your Vite / network configuration and https://vitejs.dev/config/server-options.html#server-hmr .',\n        )\n      })\n      socket.addEventListener(\n        'open',\n        () => {\n          console.info(\n            '[vite] Direct websocket connection fallback. Check out https://vitejs.dev/config/server-options.html#server-hmr to remove the previous connection error.',\n          )\n        },\n        { once: true },\n      )\n    }\n  }\n\n  socket = setupWebSocket(socketProtocol, socketHost, fallback)\n} catch (error) {\n  console.error(`[vite] failed to connect to websocket (${error}). `)\n}\n\nfunction setupWebSocket(\n  protocol: string,\n  hostAndPath: string,\n  onCloseWithoutOpen?: () => void,\n) {\n  const socket = new WebSocket(\n    `${protocol}://${hostAndPath}?token=${wsToken}`,\n    'vite-hmr',\n  )\n  let isOpened = false\n\n  socket.addEventListener(\n    'open',\n    () => {\n      isOpened = true\n      notifyListeners('vite:ws:connect', { webSocket: socket })\n    },\n    { once: true },\n  )\n\n  // Listen for messages\n  socket.addEventListener('message', async ({ data }) => {\n    handleMessage(JSON.parse(data))\n  })\n\n  // ping server\n  socket.addEventListener('close', async ({ wasClean }) => {\n    if (wasClean) return\n\n    if (!isOpened && onCloseWithoutOpen) {\n      onCloseWithoutOpen()\n      return\n    }\n\n    notifyListeners('vite:ws:disconnect', { webSocket: socket })\n\n    console.log(`[vite] server connection lost. polling for restart...`)\n    await waitForSuccessfulPing(protocol, hostAndPath)\n    location.reload()\n  })\n\n  return socket\n}\n\nfunction warnFailedFetch(err: Error, path: string | string[]) {\n  if (!err.message.match('fetch')) {\n    console.error(err)\n  }\n  console.error(\n    `[hmr] Failed to reload ${path}. ` +\n      `This could be due to syntax errors or importing non-existent ` +\n      `modules. (see errors above)`,\n  )\n}\n\nfunction cleanUrl(pathname: string): string {\n  const url = new URL(pathname, location.toString())\n  url.searchParams.delete('direct')\n  return url.pathname + url.search\n}\n\nlet isFirstUpdate = true\nconst outdatedLinkTags = new WeakSet<HTMLLinkElement>()\n\nconst debounceReload = (time: number) => {\n  let timer: ReturnType<typeof setTimeout> | null\n  return () => {\n    if (timer) {\n      clearTimeout(timer)\n      timer = null\n    }\n    timer = setTimeout(() => {\n      location.reload()\n    }, time)\n  }\n}\nconst pageReload = debounceReload(50)\n\nasync function handleMessage(payload: HMRPayload) {\n  switch (payload.type) {\n    case 'connected':\n      console.debug(`[vite] connected.`)\n      sendMessageBuffer()\n      // proxy(nginx, docker) hmr ws maybe caused timeout,\n      // so send ping package let ws keep alive.\n      setInterval(() => {\n        if (socket.readyState === socket.OPEN) {\n          socket.send('{\"type\":\"ping\"}')\n        }\n      }, __HMR_TIMEOUT__)\n      break\n    case 'update':\n      notifyListeners('vite:beforeUpdate', payload)\n      // if this is the first update and there's already an error overlay, it\n      // means the page opened with existing server compile error and the whole\n      // module script failed to load (since one of the nested imports is 500).\n      // in this case a normal update won't work and a full reload is needed.\n      if (isFirstUpdate && hasErrorOverlay()) {\n        window.location.reload()\n        return\n      } else {\n        clearErrorOverlay()\n        isFirstUpdate = false\n      }\n      await Promise.all(\n        payload.updates.map(async (update): Promise<void> => {\n          if (update.type === 'js-update') {\n            return queueUpdate(fetchUpdate(update))\n          }\n\n          // css-update\n          // this is only sent when a css file referenced with <link> is updated\n          const { path, timestamp } = update\n          const searchUrl = cleanUrl(path)\n          // can't use querySelector with `[href*=]` here since the link may be\n          // using relative paths so we need to use link.href to grab the full\n          // URL for the include check.\n          const el = Array.from(\n            document.querySelectorAll<HTMLLinkElement>('link'),\n          ).find(\n            (e) =>\n              !outdatedLinkTags.has(e) && cleanUrl(e.href).includes(searchUrl),\n          )\n\n          if (!el) {\n            return\n          }\n\n          const newPath = `${base}${searchUrl.slice(1)}${\n            searchUrl.includes('?') ? '&' : '?'\n          }t=${timestamp}`\n\n          // rather than swapping the href on the existing tag, we will\n          // create a new link tag. Once the new stylesheet has loaded we\n          // will remove the existing link tag. This removes a Flash Of\n          // Unstyled Content that can occur when swapping out the tag href\n          // directly, as the new stylesheet has not yet been loaded.\n          return new Promise((resolve) => {\n            const newLinkTag = el.cloneNode() as HTMLLinkElement\n            newLinkTag.href = new URL(newPath, el.href).href\n            const removeOldEl = () => {\n              el.remove()\n              console.debug(`[vite] css hot updated: ${searchUrl}`)\n              resolve()\n            }\n            newLinkTag.addEventListener('load', removeOldEl)\n            newLinkTag.addEventListener('error', removeOldEl)\n            outdatedLinkTags.add(el)\n            el.after(newLinkTag)\n          })\n        }),\n      )\n      notifyListeners('vite:afterUpdate', payload)\n      break\n    case 'custom': {\n      notifyListeners(payload.event, payload.data)\n      break\n    }\n    case 'full-reload':\n      notifyListeners('vite:beforeFullReload', payload)\n      if (payload.path && payload.path.endsWith('.html')) {\n        // if html file is edited, only reload the page if the browser is\n        // currently on that page.\n        const pagePath = decodeURI(location.pathname)\n        const payloadPath = base + payload.path.slice(1)\n        if (\n          pagePath === payloadPath ||\n          payload.path === '/index.html' ||\n          (pagePath.endsWith('/') && pagePath + 'index.html' === payloadPath)\n        ) {\n          pageReload()\n        }\n        return\n      } else {\n        pageReload()\n      }\n      break\n    case 'prune':\n      notifyListeners('vite:beforePrune', payload)\n      // After an HMR update, some modules are no longer imported on the page\n      // but they may have left behind side effects that need to be cleaned up\n      // (.e.g style injections)\n      // TODO Trigger their dispose callbacks.\n      payload.paths.forEach((path) => {\n        const fn = pruneMap.get(path)\n        if (fn) {\n          fn(dataMap.get(path))\n        }\n      })\n      break\n    case 'error': {\n      notifyListeners('vite:error', payload)\n      const err = payload.err\n      if (enableOverlay) {\n        createErrorOverlay(err)\n      } else {\n        console.error(\n          `[vite] Internal Server Error\\n${err.message}\\n${err.stack}`,\n        )\n      }\n      break\n    }\n    default: {\n      const check: never = payload\n      return check\n    }\n  }\n}\n\nfunction notifyListeners<T extends string>(\n  event: T,\n  data: InferCustomEventPayload<T>,\n): void\nfunction notifyListeners(event: string, data: any): void {\n  const cbs = customListenersMap.get(event)\n  if (cbs) {\n    cbs.forEach((cb) => cb(data))\n  }\n}\n\nconst enableOverlay = __HMR_ENABLE_OVERLAY__\n\nfunction createErrorOverlay(err: ErrorPayload['err']) {\n  if (!enableOverlay) return\n  clearErrorOverlay()\n  document.body.appendChild(new ErrorOverlay(err))\n}\n\nfunction clearErrorOverlay() {\n  document\n    .querySelectorAll(overlayId)\n    .forEach((n) => (n as ErrorOverlay).close())\n}\n\nfunction hasErrorOverlay() {\n  return document.querySelectorAll(overlayId).length\n}\n\nlet pending = false\nlet queued: Promise<(() => void) | undefined>[] = []\n\n/**\n * buffer multiple hot updates triggered by the same src change\n * so that they are invoked in the same order they were sent.\n * (otherwise the order may be inconsistent because of the http request round trip)\n */\nasync function queueUpdate(p: Promise<(() => void) | undefined>) {\n  queued.push(p)\n  if (!pending) {\n    pending = true\n    await Promise.resolve()\n    pending = false\n    const loading = [...queued]\n    queued = []\n    ;(await Promise.all(loading)).forEach((fn) => fn && fn())\n  }\n}\n\nasync function waitForSuccessfulPing(\n  socketProtocol: string,\n  hostAndPath: string,\n  ms = 1000,\n) {\n  const pingHostProtocol = socketProtocol === 'wss' ? 'https' : 'http'\n\n  const ping = async () => {\n    // A fetch on a websocket URL will return a successful promise with status 400,\n    // but will reject a networking error.\n    // When running on middleware mode, it returns status 426, and an cors error happens if mode is not no-cors\n    try {\n      await fetch(`${pingHostProtocol}://${hostAndPath}`, {\n        mode: 'no-cors',\n        headers: {\n          // Custom headers won't be included in a request with no-cors so (ab)use one of the\n          // safelisted headers to identify the ping request\n          Accept: 'text/x-vite-ping',\n        },\n      })\n      return true\n    } catch {}\n    return false\n  }\n\n  if (await ping()) {\n    return\n  }\n  await wait(ms)\n\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    if (document.visibilityState === 'visible') {\n      if (await ping()) {\n        break\n      }\n      await wait(ms)\n    } else {\n      await waitForWindowShow()\n    }\n  }\n}\n\nfunction wait(ms: number) {\n  return new Promise((resolve) => setTimeout(resolve, ms))\n}\n\nfunction waitForWindowShow() {\n  return new Promise<void>((resolve) => {\n    const onChange = async () => {\n      if (document.visibilityState === 'visible') {\n        resolve()\n        document.removeEventListener('visibilitychange', onChange)\n      }\n    }\n    document.addEventListener('visibilitychange', onChange)\n  })\n}\n\nconst sheetsMap = new Map<string, HTMLStyleElement>()\n\n// collect existing style elements that may have been inserted during SSR\n// to avoid FOUC or duplicate styles\nif ('document' in globalThis) {\n  document.querySelectorAll('style[data-vite-dev-id]').forEach((el) => {\n    sheetsMap.set(el.getAttribute('data-vite-dev-id')!, el as HTMLStyleElement)\n  })\n}\n\n// all css imports should be inserted at the same position\n// because after build it will be a single css file\nlet lastInsertedStyle: HTMLStyleElement | undefined\n\nexport function updateStyle(id: string, content: string): void {\n  let style = sheetsMap.get(id)\n  if (!style) {\n    style = document.createElement('style')\n    style.setAttribute('type', 'text/css')\n    style.setAttribute('data-vite-dev-id', id)\n    style.textContent = content\n\n    if (!lastInsertedStyle) {\n      document.head.appendChild(style)\n\n      // reset lastInsertedStyle after async\n      // because dynamically imported css will be splitted into a different file\n      setTimeout(() => {\n        lastInsertedStyle = undefined\n      }, 0)\n    } else {\n      lastInsertedStyle.insertAdjacentElement('afterend', style)\n    }\n    lastInsertedStyle = style\n  } else {\n    style.textContent = content\n  }\n  sheetsMap.set(id, style)\n}\n\nexport function removeStyle(id: string): void {\n  const style = sheetsMap.get(id)\n  if (style) {\n    document.head.removeChild(style)\n    sheetsMap.delete(id)\n  }\n}\n\nasync function fetchUpdate({\n  path,\n  acceptedPath,\n  timestamp,\n  explicitImportRequired,\n}: Update) {\n  const mod = hotModulesMap.get(path)\n  if (!mod) {\n    // In a code-splitting project,\n    // it is common that the hot-updating module is not loaded yet.\n    // https://github.com/vitejs/vite/issues/721\n    return\n  }\n\n  let fetchedModule: ModuleNamespace | undefined\n  const isSelfUpdate = path === acceptedPath\n\n  // determine the qualified callbacks before we re-import the modules\n  const qualifiedCallbacks = mod.callbacks.filter(({ deps }) =>\n    deps.includes(acceptedPath),\n  )\n\n  if (isSelfUpdate || qualifiedCallbacks.length > 0) {\n    const disposer = disposeMap.get(acceptedPath)\n    if (disposer) await disposer(dataMap.get(acceptedPath))\n    const [acceptedPathWithoutQuery, query] = acceptedPath.split(`?`)\n    try {\n      fetchedModule = await import(\n        /* @vite-ignore */\n        base +\n          acceptedPathWithoutQuery.slice(1) +\n          `?${explicitImportRequired ? 'import&' : ''}t=${timestamp}${\n            query ? `&${query}` : ''\n          }`\n      )\n    } catch (e) {\n      warnFailedFetch(e, acceptedPath)\n    }\n  }\n\n  return () => {\n    for (const { deps, fn } of qualifiedCallbacks) {\n      fn(deps.map((dep) => (dep === acceptedPath ? fetchedModule : undefined)))\n    }\n    const loggedPath = isSelfUpdate ? path : `${acceptedPath} via ${path}`\n    console.debug(`[vite] hot updated: ${loggedPath}`)\n  }\n}\n\nfunction sendMessageBuffer() {\n  if (socket.readyState === 1) {\n    messageBuffer.forEach((msg) => socket.send(msg))\n    messageBuffer.length = 0\n  }\n}\n\ninterface HotModule {\n  id: string\n  callbacks: HotCallback[]\n}\n\ninterface HotCallback {\n  // the dependencies must be fetchable paths\n  deps: string[]\n  fn: (modules: Array<ModuleNamespace | undefined>) => void\n}\n\ntype CustomListenersMap = Map<string, ((data: any) => void)[]>\n\nconst hotModulesMap = new Map<string, HotModule>()\nconst disposeMap = new Map<string, (data: any) => void | Promise<void>>()\nconst pruneMap = new Map<string, (data: any) => void | Promise<void>>()\nconst dataMap = new Map<string, any>()\nconst customListenersMap: CustomListenersMap = new Map()\nconst ctxToListenersMap = new Map<string, CustomListenersMap>()\n\nexport function createHotContext(ownerPath: string): ViteHotContext {\n  if (!dataMap.has(ownerPath)) {\n    dataMap.set(ownerPath, {})\n  }\n\n  // when a file is hot updated, a new context is created\n  // clear its stale callbacks\n  const mod = hotModulesMap.get(ownerPath)\n  if (mod) {\n    mod.callbacks = []\n  }\n\n  // clear stale custom event listeners\n  const staleListeners = ctxToListenersMap.get(ownerPath)\n  if (staleListeners) {\n    for (const [event, staleFns] of staleListeners) {\n      const listeners = customListenersMap.get(event)\n      if (listeners) {\n        customListenersMap.set(\n          event,\n          listeners.filter((l) => !staleFns.includes(l)),\n        )\n      }\n    }\n  }\n\n  const newListeners: CustomListenersMap = new Map()\n  ctxToListenersMap.set(ownerPath, newListeners)\n\n  function acceptDeps(deps: string[], callback: HotCallback['fn'] = () => {}) {\n    const mod: HotModule = hotModulesMap.get(ownerPath) || {\n      id: ownerPath,\n      callbacks: [],\n    }\n    mod.callbacks.push({\n      deps,\n      fn: callback,\n    })\n    hotModulesMap.set(ownerPath, mod)\n  }\n\n  const hot: ViteHotContext = {\n    get data() {\n      return dataMap.get(ownerPath)\n    },\n\n    accept(deps?: any, callback?: any) {\n      if (typeof deps === 'function' || !deps) {\n        // self-accept: hot.accept(() => {})\n        acceptDeps([ownerPath], ([mod]) => deps?.(mod))\n      } else if (typeof deps === 'string') {\n        // explicit deps\n        acceptDeps([deps], ([mod]) => callback?.(mod))\n      } else if (Array.isArray(deps)) {\n        acceptDeps(deps, callback)\n      } else {\n        throw new Error(`invalid hot.accept() usage.`)\n      }\n    },\n\n    // export names (first arg) are irrelevant on the client side, they're\n    // extracted in the server for propagation\n    acceptExports(_, callback) {\n      acceptDeps([ownerPath], ([mod]) => callback?.(mod))\n    },\n\n    dispose(cb) {\n      disposeMap.set(ownerPath, cb)\n    },\n\n    prune(cb) {\n      pruneMap.set(ownerPath, cb)\n    },\n\n    // Kept for backward compatibility (#11036)\n    // @ts-expect-error untyped\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    decline() {},\n\n    // tell the server to re-perform hmr propagation from this module as root\n    invalidate(message) {\n      notifyListeners('vite:invalidate', { path: ownerPath, message })\n      this.send('vite:invalidate', { path: ownerPath, message })\n      console.debug(\n        `[vite] invalidate ${ownerPath}${message ? `: ${message}` : ''}`,\n      )\n    },\n\n    // custom events\n    on(event, cb) {\n      const addToMap = (map: Map<string, any[]>) => {\n        const existing = map.get(event) || []\n        existing.push(cb)\n        map.set(event, existing)\n      }\n      addToMap(customListenersMap)\n      addToMap(newListeners)\n    },\n\n    send(event, data) {\n      messageBuffer.push(JSON.stringify({ type: 'custom', event, data }))\n      sendMessageBuffer()\n    },\n  }\n\n  return hot\n}\n\n/**\n * urls here are dynamic import() urls that couldn't be statically analyzed\n */\nexport function injectQuery(url: string, queryToInject: string): string {\n  // skip urls that won't be handled by vite\n  if (url[0] !== '.' && url[0] !== '/') {\n    return url\n  }\n\n  // can't use pathname from URL since it may be relative like ../\n  const pathname = url.replace(/#.*$/, '').replace(/\\?.*$/, '')\n  const { search, hash } = new URL(url, 'http://vitejs.dev')\n\n  return `${pathname}?${queryToInject}${search ? `&` + search.slice(1) : ''}${\n    hash || ''\n  }`\n}\n\nexport { ErrorOverlay }\n"], "names": ["base"], "mappings": ";;AAKA,MAAMA,MAAI,GAAG,QAAQ,IAAI,GAAG,CAAA;AAE5B;AACA,MAAM,QAAQ,YAAY,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4IzB,CAAA;AAED,MAAM,MAAM,GAAG,gCAAgC,CAAA;AAC/C,MAAM,WAAW,GAAG,0CAA0C,CAAA;AAE9D;AACA;AACA,MAAM,EAAE,WAAW,GAAG,MAAA;CAAyC,EAAE,GAAG,UAAU,CAAA;AACxE,MAAO,YAAa,SAAQ,WAAW,CAAA;AAI3C,IAAA,WAAA,CAAY,GAAwB,EAAE,KAAK,GAAG,IAAI,EAAA;;AAChD,QAAA,KAAK,EAAE,CAAA;AACP,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAA;AAC/C,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;AAE9B,QAAA,WAAW,CAAC,SAAS,GAAG,CAAC,CAAA;AACzB,QAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QACzD,MAAM,OAAO,GAAG,QAAQ;cACpB,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;AACtC,cAAE,GAAG,CAAC,OAAO,CAAA;QACf,IAAI,GAAG,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAW,QAAA,EAAA,GAAG,CAAC,MAAM,CAAI,EAAA,CAAA,CAAC,CAAA;AAChD,SAAA;QACD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;QAE1C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,EAAA,GAAA,GAAG,CAAC,GAAG,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,KAAI,GAAG,CAAC,EAAE,IAAI,cAAc,EAAE,KAAK,CAAC,CAAG,CAAA,CAAA,CAAC,CAAA;QACrE,IAAI,GAAG,CAAC,GAAG,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAG,EAAA,IAAI,CAAI,CAAA,EAAA,GAAG,CAAC,GAAG,CAAC,IAAI,CAAA,CAAA,EAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAE,CAAA,EAAE,KAAK,CAAC,CAAA;AACvE,SAAA;aAAM,IAAI,GAAG,CAAC,EAAE,EAAE;AACjB,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AACzB,SAAA;AAED,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAM,CAAC,IAAI,EAAE,CAAC,CAAA;AACvC,SAAA;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;AAErC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,KAAI;YAClE,CAAC,CAAC,eAAe,EAAE,CAAA;AACrB,SAAC,CAAC,CAAA;AAEF,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;YAClC,IAAI,CAAC,KAAK,EAAE,CAAA;AACd,SAAC,CAAC,CAAA;AAEF,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAgB,KAAI;YACrC,IAAI,CAAC,CAAC,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC7C,IAAI,CAAC,KAAK,EAAE,CAAA;AACb,aAAA;AACH,SAAC,CAAA;QAED,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;KACtD;AAED,IAAA,IAAI,CAAC,QAAgB,EAAE,IAAY,EAAE,SAAS,GAAG,KAAK,EAAA;QACpD,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAE,CAAA;QAC7C,IAAI,CAAC,SAAS,EAAE;AACd,YAAA,EAAE,CAAC,WAAW,GAAG,IAAI,CAAA;AACtB,SAAA;AAAM,aAAA;YACL,IAAI,QAAQ,GAAG,CAAC,CAAA;AAChB,YAAA,IAAI,KAA6B,CAAA;AACjC,YAAA,MAAM,CAAC,SAAS,GAAG,CAAC,CAAA;YACpB,QAAQ,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBAClC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,CAAA;gBAChC,IAAI,KAAK,IAAI,IAAI,EAAE;oBACjB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;oBACxC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAA;oBAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;AACxC,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;AACvB,oBAAA,IAAI,CAAC,SAAS,GAAG,WAAW,CAAA;AAC5B,oBAAA,IAAI,CAAC,OAAO,GAAG,MAAK;wBAClB,KAAK,CAAC,CAAG,EAAAA,MAAI,CAAwB,sBAAA,CAAA,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAA;AACnE,qBAAC,CAAA;AACD,oBAAA,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;oBACpB,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;AACtC,iBAAA;AACF,aAAA;AACF,SAAA;KACF;IACD,KAAK,GAAA;;QACH,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,IAAI,CAAC,CAAA;QAClC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;KACzD;AACF,CAAA;AAEM,MAAM,SAAS,GAAG,oBAAoB,CAAA;AAC7C,MAAM,EAAE,cAAc,EAAE,GAAG,UAAU,CAAA;AACrC,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AACpD,IAAA,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,CAAA;AAC/C;;AC7ND,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;AAErC,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAE9C;AACA,MAAM,UAAU,GAAG,eAAe,CAAA;AAClC,MAAM,cAAc,GAClB,gBAAgB,KAAK,aAAa,CAAC,QAAQ,KAAK,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,CAAA;AAC1E,MAAM,OAAO,GAAG,YAAY,CAAA;AAC5B,MAAM,UAAU,GAAG,CAAA,EAAG,gBAAgB,IAAI,aAAa,CAAC,QAAQ,CAC9D,CAAA,EAAA,OAAO,IAAI,aAAa,CAAC,IAC3B,CAAG,EAAA,YAAY,EAAE,CAAA;AACjB,MAAM,gBAAgB,GAAG,qBAAqB,CAAA;AAC9C,MAAM,IAAI,GAAG,QAAQ,IAAI,GAAG,CAAA;AAC5B,MAAM,OAAO,GAAG,YAAY,CAAA;AAC5B,MAAM,aAAa,GAAa,EAAE,CAAA;AAElC,IAAI,MAAiB,CAAA;AACrB,IAAI;AACF,IAAA,IAAI,QAAkC,CAAA;;IAEtC,IAAI,CAAC,OAAO,EAAE;QACZ,QAAQ,GAAG,MAAK;;;YAGd,MAAM,GAAG,cAAc,CAAC,cAAc,EAAE,gBAAgB,EAAE,MAAK;gBAC7D,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACrD,gBAAA,MAAM,iBAAiB,GACrB,oBAAoB,CAAC,IAAI;oBACzB,oBAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAA;gBAC7D,OAAO,CAAC,KAAK,CACX,0CAA0C;oBACxC,uBAAuB;oBACvB,CAAe,YAAA,EAAA,iBAAiB,CAAiB,cAAA,EAAA,UAAU,CAAa,WAAA,CAAA;oBACxE,CAAe,YAAA,EAAA,UAAU,CAAgC,6BAAA,EAAA,gBAAgB,CAAa,WAAA,CAAA;AACtF,oBAAA,4GAA4G,CAC/G,CAAA;AACH,aAAC,CAAC,CAAA;AACF,YAAA,MAAM,CAAC,gBAAgB,CACrB,MAAM,EACN,MAAK;AACH,gBAAA,OAAO,CAAC,IAAI,CACV,0JAA0J,CAC3J,CAAA;AACH,aAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAA;AACH,SAAC,CAAA;AACF,KAAA;IAED,MAAM,GAAG,cAAc,CAAC,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAA;AAC9D,CAAA;AAAC,OAAO,KAAK,EAAE;AACd,IAAA,OAAO,CAAC,KAAK,CAAC,0CAA0C,KAAK,CAAA,GAAA,CAAK,CAAC,CAAA;AACpE,CAAA;AAED,SAAS,cAAc,CACrB,QAAgB,EAChB,WAAmB,EACnB,kBAA+B,EAAA;AAE/B,IAAA,MAAM,MAAM,GAAG,IAAI,SAAS,CAC1B,CAAG,EAAA,QAAQ,CAAM,GAAA,EAAA,WAAW,UAAU,OAAO,CAAA,CAAE,EAC/C,UAAU,CACX,CAAA;IACD,IAAI,QAAQ,GAAG,KAAK,CAAA;AAEpB,IAAA,MAAM,CAAC,gBAAgB,CACrB,MAAM,EACN,MAAK;QACH,QAAQ,GAAG,IAAI,CAAA;QACf,eAAe,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;AAC3D,KAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAA;;IAGD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,KAAI;QACpD,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;AACjC,KAAC,CAAC,CAAA;;IAGF,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAI;AACtD,QAAA,IAAI,QAAQ;YAAE,OAAM;AAEpB,QAAA,IAAI,CAAC,QAAQ,IAAI,kBAAkB,EAAE;AACnC,YAAA,kBAAkB,EAAE,CAAA;YACpB,OAAM;AACP,SAAA;QAED,eAAe,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;AAE5D,QAAA,OAAO,CAAC,GAAG,CAAC,CAAA,qDAAA,CAAuD,CAAC,CAAA;AACpE,QAAA,MAAM,qBAAqB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;QAClD,QAAQ,CAAC,MAAM,EAAE,CAAA;AACnB,KAAC,CAAC,CAAA;AAEF,IAAA,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,eAAe,CAAC,GAAU,EAAE,IAAuB,EAAA;IAC1D,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AAC/B,QAAA,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AACnB,KAAA;AACD,IAAA,OAAO,CAAC,KAAK,CACX,CAAA,uBAAA,EAA0B,IAAI,CAAI,EAAA,CAAA;QAChC,CAA+D,6DAAA,CAAA;AAC/D,QAAA,CAAA,2BAAA,CAA6B,CAChC,CAAA;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,QAAgB,EAAA;AAChC,IAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAA;AAClD,IAAA,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;AACjC,IAAA,OAAO,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAA;AAClC,CAAC;AAED,IAAI,aAAa,GAAG,IAAI,CAAA;AACxB,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAmB,CAAA;AAEvD,MAAM,cAAc,GAAG,CAAC,IAAY,KAAI;AACtC,IAAA,IAAI,KAA2C,CAAA;AAC/C,IAAA,OAAO,MAAK;AACV,QAAA,IAAI,KAAK,EAAE;YACT,YAAY,CAAC,KAAK,CAAC,CAAA;YACnB,KAAK,GAAG,IAAI,CAAA;AACb,SAAA;AACD,QAAA,KAAK,GAAG,UAAU,CAAC,MAAK;YACtB,QAAQ,CAAC,MAAM,EAAE,CAAA;SAClB,EAAE,IAAI,CAAC,CAAA;AACV,KAAC,CAAA;AACH,CAAC,CAAA;AACD,MAAM,UAAU,GAAG,cAAc,CAAC,EAAE,CAAC,CAAA;AAErC,eAAe,aAAa,CAAC,OAAmB,EAAA;IAC9C,QAAQ,OAAO,CAAC,IAAI;AAClB,QAAA,KAAK,WAAW;AACd,YAAA,OAAO,CAAC,KAAK,CAAC,CAAA,iBAAA,CAAmB,CAAC,CAAA;AAClC,YAAA,iBAAiB,EAAE,CAAA;;;YAGnB,WAAW,CAAC,MAAK;AACf,gBAAA,IAAI,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,EAAE;AACrC,oBAAA,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;AAC/B,iBAAA;aACF,EAAE,eAAe,CAAC,CAAA;YACnB,MAAK;AACP,QAAA,KAAK,QAAQ;AACX,YAAA,eAAe,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAA;;;;;AAK7C,YAAA,IAAI,aAAa,IAAI,eAAe,EAAE,EAAE;AACtC,gBAAA,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAA;gBACxB,OAAM;AACP,aAAA;AAAM,iBAAA;AACL,gBAAA,iBAAiB,EAAE,CAAA;gBACnB,aAAa,GAAG,KAAK,CAAA;AACtB,aAAA;AACD,YAAA,MAAM,OAAO,CAAC,GAAG,CACf,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,KAAmB;AAClD,gBAAA,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;AAC/B,oBAAA,OAAO,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAA;AACxC,iBAAA;;;AAID,gBAAA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA;AAClC,gBAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;;;;AAIhC,gBAAA,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,CACnB,QAAQ,CAAC,gBAAgB,CAAkB,MAAM,CAAC,CACnD,CAAC,IAAI,CACJ,CAAC,CAAC,KACA,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CACnE,CAAA;gBAED,IAAI,CAAC,EAAE,EAAE;oBACP,OAAM;AACP,iBAAA;AAED,gBAAA,MAAM,OAAO,GAAG,CAAG,EAAA,IAAI,CAAG,EAAA,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA,EAC1C,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAClC,CAAK,EAAA,EAAA,SAAS,EAAE,CAAA;;;;;;AAOhB,gBAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;AAC7B,oBAAA,MAAM,UAAU,GAAG,EAAE,CAAC,SAAS,EAAqB,CAAA;AACpD,oBAAA,UAAU,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAA;oBAChD,MAAM,WAAW,GAAG,MAAK;wBACvB,EAAE,CAAC,MAAM,EAAE,CAAA;AACX,wBAAA,OAAO,CAAC,KAAK,CAAC,2BAA2B,SAAS,CAAA,CAAE,CAAC,CAAA;AACrD,wBAAA,OAAO,EAAE,CAAA;AACX,qBAAC,CAAA;AACD,oBAAA,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;AAChD,oBAAA,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;AACjD,oBAAA,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;AACxB,oBAAA,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;AACtB,iBAAC,CAAC,CAAA;aACH,CAAC,CACH,CAAA;AACD,YAAA,eAAe,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;YAC5C,MAAK;QACP,KAAK,QAAQ,EAAE;YACb,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;YAC5C,MAAK;AACN,SAAA;AACD,QAAA,KAAK,aAAa;AAChB,YAAA,eAAe,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAA;AACjD,YAAA,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;;;gBAGlD,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;AAC7C,gBAAA,MAAM,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAChD,IACE,QAAQ,KAAK,WAAW;oBACxB,OAAO,CAAC,IAAI,KAAK,aAAa;AAC9B,qBAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,GAAG,YAAY,KAAK,WAAW,CAAC,EACnE;AACA,oBAAA,UAAU,EAAE,CAAA;AACb,iBAAA;gBACD,OAAM;AACP,aAAA;AAAM,iBAAA;AACL,gBAAA,UAAU,EAAE,CAAA;AACb,aAAA;YACD,MAAK;AACP,QAAA,KAAK,OAAO;AACV,YAAA,eAAe,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;;;;;YAK5C,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;gBAC7B,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;AAC7B,gBAAA,IAAI,EAAE,EAAE;oBACN,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;AACtB,iBAAA;AACH,aAAC,CAAC,CAAA;YACF,MAAK;QACP,KAAK,OAAO,EAAE;AACZ,YAAA,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;AACtC,YAAA,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;AACvB,YAAA,IAAI,aAAa,EAAE;gBACjB,kBAAkB,CAAC,GAAG,CAAC,CAAA;AACxB,aAAA;AAAM,iBAAA;AACL,gBAAA,OAAO,CAAC,KAAK,CACX,CAAA,8BAAA,EAAiC,GAAG,CAAC,OAAO,CAAA,EAAA,EAAK,GAAG,CAAC,KAAK,CAAA,CAAE,CAC7D,CAAA;AACF,aAAA;YACD,MAAK;AACN,SAAA;AACD,QAAA,SAAS;YACP,MAAM,KAAK,GAAU,OAAO,CAAA;AAC5B,YAAA,OAAO,KAAK,CAAA;AACb,SAAA;AACF,KAAA;AACH,CAAC;AAMD,SAAS,eAAe,CAAC,KAAa,EAAE,IAAS,EAAA;IAC/C,MAAM,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AACzC,IAAA,IAAI,GAAG,EAAE;AACP,QAAA,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;AAC9B,KAAA;AACH,CAAC;AAED,MAAM,aAAa,GAAG,sBAAsB,CAAA;AAE5C,SAAS,kBAAkB,CAAC,GAAwB,EAAA;AAClD,IAAA,IAAI,CAAC,aAAa;QAAE,OAAM;AAC1B,IAAA,iBAAiB,EAAE,CAAA;IACnB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,CAAA;AAClD,CAAC;AAED,SAAS,iBAAiB,GAAA;IACxB,QAAQ;SACL,gBAAgB,CAAC,SAAS,CAAC;SAC3B,OAAO,CAAC,CAAC,CAAC,KAAM,CAAkB,CAAC,KAAK,EAAE,CAAC,CAAA;AAChD,CAAC;AAED,SAAS,eAAe,GAAA;IACtB,OAAO,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAA;AACpD,CAAC;AAED,IAAI,OAAO,GAAG,KAAK,CAAA;AACnB,IAAI,MAAM,GAAwC,EAAE,CAAA;AAEpD;;;;AAIG;AACH,eAAe,WAAW,CAAC,CAAoC,EAAA;AAC7D,IAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACd,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,GAAG,IAAI,CAAA;AACd,QAAA,MAAM,OAAO,CAAC,OAAO,EAAE,CAAA;QACvB,OAAO,GAAG,KAAK,CAAA;AACf,QAAA,MAAM,OAAO,GAAG,CAAC,GAAG,MAAM,CAAC,CAAA;QAC3B,MAAM,GAAG,EAAE,CACV;QAAA,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;AAC1D,KAAA;AACH,CAAC;AAED,eAAe,qBAAqB,CAClC,cAAsB,EACtB,WAAmB,EACnB,EAAE,GAAG,IAAI,EAAA;AAET,IAAA,MAAM,gBAAgB,GAAG,cAAc,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM,CAAA;AAEpE,IAAA,MAAM,IAAI,GAAG,YAAW;;;;QAItB,IAAI;AACF,YAAA,MAAM,KAAK,CAAC,CAAA,EAAG,gBAAgB,CAAM,GAAA,EAAA,WAAW,EAAE,EAAE;AAClD,gBAAA,IAAI,EAAE,SAAS;AACf,gBAAA,OAAO,EAAE;;;AAGP,oBAAA,MAAM,EAAE,kBAAkB;AAC3B,iBAAA;AACF,aAAA,CAAC,CAAA;AACF,YAAA,OAAO,IAAI,CAAA;AACZ,SAAA;AAAC,QAAA,MAAM,GAAE;AACV,QAAA,OAAO,KAAK,CAAA;AACd,KAAC,CAAA;IAED,IAAI,MAAM,IAAI,EAAE,EAAE;QAChB,OAAM;AACP,KAAA;AACD,IAAA,MAAM,IAAI,CAAC,EAAE,CAAC,CAAA;;AAGd,IAAA,OAAO,IAAI,EAAE;AACX,QAAA,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE;YAC1C,IAAI,MAAM,IAAI,EAAE,EAAE;gBAChB,MAAK;AACN,aAAA;AACD,YAAA,MAAM,IAAI,CAAC,EAAE,CAAC,CAAA;AACf,SAAA;AAAM,aAAA;YACL,MAAM,iBAAiB,EAAE,CAAA;AAC1B,SAAA;AACF,KAAA;AACH,CAAC;AAED,SAAS,IAAI,CAAC,EAAU,EAAA;AACtB,IAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;AAC1D,CAAC;AAED,SAAS,iBAAiB,GAAA;AACxB,IAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;AACnC,QAAA,MAAM,QAAQ,GAAG,YAAW;AAC1B,YAAA,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE;AAC1C,gBAAA,OAAO,EAAE,CAAA;AACT,gBAAA,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAA;AAC3D,aAAA;AACH,SAAC,CAAA;AACD,QAAA,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAA;AACzD,KAAC,CAAC,CAAA;AACJ,CAAC;AAED,MAAM,SAAS,GAAG,IAAI,GAAG,EAA4B,CAAA;AAErD;AACA;AACA,IAAI,UAAU,IAAI,UAAU,EAAE;IAC5B,QAAQ,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;AAClE,QAAA,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,kBAAkB,CAAE,EAAE,EAAsB,CAAC,CAAA;AAC7E,KAAC,CAAC,CAAA;AACH,CAAA;AAED;AACA;AACA,IAAI,iBAA+C,CAAA;AAEnC,SAAA,WAAW,CAAC,EAAU,EAAE,OAAe,EAAA;IACrD,IAAI,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAC7B,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;AACvC,QAAA,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;AACtC,QAAA,KAAK,CAAC,YAAY,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAA;AAC1C,QAAA,KAAK,CAAC,WAAW,GAAG,OAAO,CAAA;QAE3B,IAAI,CAAC,iBAAiB,EAAE;AACtB,YAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;;;YAIhC,UAAU,CAAC,MAAK;gBACd,iBAAiB,GAAG,SAAS,CAAA;aAC9B,EAAE,CAAC,CAAC,CAAA;AACN,SAAA;AAAM,aAAA;AACL,YAAA,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;AAC3D,SAAA;QACD,iBAAiB,GAAG,KAAK,CAAA;AAC1B,KAAA;AAAM,SAAA;AACL,QAAA,KAAK,CAAC,WAAW,GAAG,OAAO,CAAA;AAC5B,KAAA;AACD,IAAA,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;AAC1B,CAAC;AAEK,SAAU,WAAW,CAAC,EAAU,EAAA;IACpC,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;AAC/B,IAAA,IAAI,KAAK,EAAE;AACT,QAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;AAChC,QAAA,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;AACrB,KAAA;AACH,CAAC;AAED,eAAe,WAAW,CAAC,EACzB,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,sBAAsB,GACf,EAAA;IACP,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACnC,IAAI,CAAC,GAAG,EAAE;;;;QAIR,OAAM;AACP,KAAA;AAED,IAAA,IAAI,aAA0C,CAAA;AAC9C,IAAA,MAAM,YAAY,GAAG,IAAI,KAAK,YAAY,CAAA;;IAG1C,MAAM,kBAAkB,GAAG,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,KACvD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAC5B,CAAA;AAED,IAAA,IAAI,YAAY,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;QACjD,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;AAC7C,QAAA,IAAI,QAAQ;YAAE,MAAM,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAA;AACvD,QAAA,MAAM,CAAC,wBAAwB,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAG,CAAA,CAAA,CAAC,CAAA;QACjE,IAAI;YACF,aAAa,GAAG,MAAM;;YAEpB,IAAI;AACF,gBAAA,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;gBACjC,CAAI,CAAA,EAAA,sBAAsB,GAAG,SAAS,GAAG,EAAE,CAAA,EAAA,EAAK,SAAS,CAAA,EACvD,KAAK,GAAG,CAAA,CAAA,EAAI,KAAK,CAAA,CAAE,GAAG,EACxB,CAAE,CAAA,CACL,CAAA;AACF,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACV,YAAA,eAAe,CAAC,CAAC,EAAE,YAAY,CAAC,CAAA;AACjC,SAAA;AACF,KAAA;AAED,IAAA,OAAO,MAAK;QACV,KAAK,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,kBAAkB,EAAE;YAC7C,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,YAAY,GAAG,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;AAC1E,SAAA;AACD,QAAA,MAAM,UAAU,GAAG,YAAY,GAAG,IAAI,GAAG,CAAG,EAAA,YAAY,CAAQ,KAAA,EAAA,IAAI,EAAE,CAAA;AACtE,QAAA,OAAO,CAAC,KAAK,CAAC,uBAAuB,UAAU,CAAA,CAAE,CAAC,CAAA;AACpD,KAAC,CAAA;AACH,CAAC;AAED,SAAS,iBAAiB,GAAA;AACxB,IAAA,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC,EAAE;AAC3B,QAAA,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;AAChD,QAAA,aAAa,CAAC,MAAM,GAAG,CAAC,CAAA;AACzB,KAAA;AACH,CAAC;AAeD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAqB,CAAA;AAClD,MAAM,UAAU,GAAG,IAAI,GAAG,EAA+C,CAAA;AACzE,MAAM,QAAQ,GAAG,IAAI,GAAG,EAA+C,CAAA;AACvE,MAAM,OAAO,GAAG,IAAI,GAAG,EAAe,CAAA;AACtC,MAAM,kBAAkB,GAAuB,IAAI,GAAG,EAAE,CAAA;AACxD,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAA8B,CAAA;AAEzD,SAAU,gBAAgB,CAAC,SAAiB,EAAA;AAChD,IAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AAC3B,QAAA,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;AAC3B,KAAA;;;IAID,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;AACxC,IAAA,IAAI,GAAG,EAAE;AACP,QAAA,GAAG,CAAC,SAAS,GAAG,EAAE,CAAA;AACnB,KAAA;;IAGD,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;AACvD,IAAA,IAAI,cAAc,EAAE;QAClB,KAAK,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,cAAc,EAAE;YAC9C,MAAM,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AAC/C,YAAA,IAAI,SAAS,EAAE;gBACb,kBAAkB,CAAC,GAAG,CACpB,KAAK,EACL,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC/C,CAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,MAAM,YAAY,GAAuB,IAAI,GAAG,EAAE,CAAA;AAClD,IAAA,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAA;IAE9C,SAAS,UAAU,CAAC,IAAc,EAAE,WAA8B,SAAQ,EAAA;QACxE,MAAM,GAAG,GAAc,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI;AACrD,YAAA,EAAE,EAAE,SAAS;AACb,YAAA,SAAS,EAAE,EAAE;SACd,CAAA;AACD,QAAA,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACjB,IAAI;AACJ,YAAA,EAAE,EAAE,QAAQ;AACb,SAAA,CAAC,CAAA;AACF,QAAA,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;KAClC;AAED,IAAA,MAAM,GAAG,GAAmB;AAC1B,QAAA,IAAI,IAAI,GAAA;AACN,YAAA,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;SAC9B;QAED,MAAM,CAAC,IAAU,EAAE,QAAc,EAAA;AAC/B,YAAA,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,EAAE;;gBAEvC,UAAU,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,aAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAG,GAAG,CAAC,CAAC,CAAA;AAChD,aAAA;AAAM,iBAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;gBAEnC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,QAAQ,aAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAG,GAAG,CAAC,CAAC,CAAA;AAC/C,aAAA;AAAM,iBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC9B,gBAAA,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;AAC3B,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,IAAI,KAAK,CAAC,CAAA,2BAAA,CAA6B,CAAC,CAAA;AAC/C,aAAA;SACF;;;QAID,aAAa,CAAC,CAAC,EAAE,QAAQ,EAAA;YACvB,UAAU,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,QAAQ,aAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAG,GAAG,CAAC,CAAC,CAAA;SACpD;AAED,QAAA,OAAO,CAAC,EAAE,EAAA;AACR,YAAA,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;SAC9B;AAED,QAAA,KAAK,CAAC,EAAE,EAAA;AACN,YAAA,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;SAC5B;;;;AAKD,QAAA,OAAO,MAAK;;AAGZ,QAAA,UAAU,CAAC,OAAO,EAAA;YAChB,eAAe,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAA;AAChE,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAA;AAC1D,YAAA,OAAO,CAAC,KAAK,CACX,qBAAqB,SAAS,CAAA,EAAG,OAAO,GAAG,CAAK,EAAA,EAAA,OAAO,EAAE,GAAG,EAAE,CAAA,CAAE,CACjE,CAAA;SACF;;QAGD,EAAE,CAAC,KAAK,EAAE,EAAE,EAAA;AACV,YAAA,MAAM,QAAQ,GAAG,CAAC,GAAuB,KAAI;gBAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;AACrC,gBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AACjB,gBAAA,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;AAC1B,aAAC,CAAA;YACD,QAAQ,CAAC,kBAAkB,CAAC,CAAA;YAC5B,QAAQ,CAAC,YAAY,CAAC,CAAA;SACvB;QAED,IAAI,CAAC,KAAK,EAAE,IAAI,EAAA;AACd,YAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;AACnE,YAAA,iBAAiB,EAAE,CAAA;SACpB;KACF,CAAA;AAED,IAAA,OAAO,GAAG,CAAA;AACZ,CAAC;AAED;;AAEG;AACa,SAAA,WAAW,CAAC,GAAW,EAAE,aAAqB,EAAA;;AAE5D,IAAA,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACpC,QAAA,OAAO,GAAG,CAAA;AACX,KAAA;;AAGD,IAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;AAC7D,IAAA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAA;IAE1D,OAAO,CAAA,EAAG,QAAQ,CAAA,CAAA,EAAI,aAAa,CAAA,EAAG,MAAM,GAAG,CAAG,CAAA,CAAA,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA,EACvE,IAAI,IAAI,EACV,CAAA,CAAE,CAAA;AACJ;;;;", "x_google_ignoreList": [0, 1]}
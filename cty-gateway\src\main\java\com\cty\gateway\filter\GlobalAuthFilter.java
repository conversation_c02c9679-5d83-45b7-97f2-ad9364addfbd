package com.cty.gateway.filter;

import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 全局认证过滤器
 */
@Component
public class GlobalAuthFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        
        // 打印请求信息（调试用）
        System.out.println("Gateway处理请求: " + request.getMethod() + " " + path);
        
        // 这里可以添加认证逻辑
        // 暂时放行所有请求
        
        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        return -100; // 优先级高
    }
}

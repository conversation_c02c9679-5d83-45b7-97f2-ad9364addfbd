import request from '@/utils/request'

// 获取城市列表
export function getCityList(params) {
  return request({
    url: '/admin/city/list',
    method: 'get',
    params
  })
}

// 获取子城市列表
export function getChildCities(parentId) {
  return request({
    url: `/admin/city/children/${parentId}`,
    method: 'get'
  })
}

// 获取热门城市
export function getHotCities() {
  return request({
    url: '/admin/city/hot',
    method: 'get'
  })
}

// 获取已开通服务的城市
export function getServiceEnabledCities() {
  return request({
    url: '/admin/city/service-enabled',
    method: 'get'
  })
}

// 获取城市树形结构
export function getCityTree() {
  return request({
    url: '/admin/city/tree',
    method: 'get'
  })
}

// 添加城市
export function addCity(data) {
  return request({
    url: '/admin/city/add',
    method: 'post',
    data
  })
}

// 更新城市
export function updateCity(data) {
  return request({
    url: '/admin/city/update',
    method: 'put',
    data
  })
}

// 删除城市
export function deleteCity(id) {
  return request({
    url: `/admin/city/delete/${id}`,
    method: 'delete'
  })
}

// 更新城市状态
export function updateCityStatus(data) {
  return request({
    url: '/admin/city/status',
    method: 'put',
    params: data
  })
}

// 更新服务状态
export function updateServiceStatus(data) {
  return request({
    url: '/admin/city/service-status',
    method: 'put',
    params: data
  })
}

// 更新热门状态
export function updateHotStatus(data) {
  return request({
    url: '/admin/city/hot-status',
    method: 'put',
    params: data
  })
}

// 根据城市编码查询城市
export function getCityByCode(cityCode) {
  return request({
    url: `/admin/city/code/${cityCode}`,
    method: 'get'
  })
}

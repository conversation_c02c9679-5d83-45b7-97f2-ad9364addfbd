var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name2 in all)
    __defProp(target, name2, { get: all[name2], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var stdin_exports = {};
__export(stdin_exports, {
  default: () => stdin_default,
  pickerToolbarPropKeys: () => pickerToolbarPropKeys,
  pickerToolbarProps: () => pickerToolbarProps,
  pickerToolbarSlots: () => pickerToolbarSlots
});
module.exports = __toCommonJS(stdin_exports);
var import_vue = require("vue");
var import_utils = require("./utils");
var import_utils2 = require("../utils");
const [name] = (0, import_utils2.createNamespace)("picker-toolbar");
const pickerToolbarProps = {
  title: String,
  cancelButtonText: String,
  confirmButtonText: String
};
const pickerToolbarSlots = ["cancel", "confirm", "title", "toolbar"];
const pickerToolbarPropKeys = Object.keys(pickerToolbarProps);
var stdin_default = (0, import_vue.defineComponent)({
  name,
  props: pickerToolbarProps,
  emits: ["confirm", "cancel"],
  setup(props, {
    emit,
    slots
  }) {
    const renderTitle = () => {
      if (slots.title) {
        return slots.title();
      }
      if (props.title) {
        return (0, import_vue.createVNode)("div", {
          "class": [(0, import_utils.bem)("title"), "van-ellipsis"]
        }, [props.title]);
      }
    };
    const onCancel = () => emit("cancel");
    const onConfirm = () => emit("confirm");
    const renderCancel = () => {
      var _a;
      const text = (_a = props.cancelButtonText) != null ? _a : (0, import_utils.t)("cancel");
      if (!slots.cancel && !text) {
        return;
      }
      return (0, import_vue.createVNode)("button", {
        "type": "button",
        "class": [(0, import_utils.bem)("cancel"), import_utils2.HAPTICS_FEEDBACK],
        "onClick": onCancel
      }, [slots.cancel ? slots.cancel() : text]);
    };
    const renderConfirm = () => {
      var _a;
      const text = (_a = props.confirmButtonText) != null ? _a : (0, import_utils.t)("confirm");
      if (!slots.confirm && !text) {
        return;
      }
      return (0, import_vue.createVNode)("button", {
        "type": "button",
        "class": [(0, import_utils.bem)("confirm"), import_utils2.HAPTICS_FEEDBACK],
        "onClick": onConfirm
      }, [slots.confirm ? slots.confirm() : text]);
    };
    return () => (0, import_vue.createVNode)("div", {
      "class": (0, import_utils.bem)("toolbar")
    }, [slots.toolbar ? slots.toolbar() : [renderCancel(), renderTitle(), renderConfirm()]]);
  }
});

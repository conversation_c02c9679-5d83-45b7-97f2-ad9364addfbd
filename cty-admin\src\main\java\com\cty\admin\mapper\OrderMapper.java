package com.cty.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cty.admin.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 订单Mapper接口
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {
    
    /**
     * 分页查询订单列表
     */
    Page<Order> selectOrderPage(Page<Order> page, 
                               @Param("orderNo") String orderNo,
                               @Param("status") Integer status, 
                               @Param("startTime") String startTime, 
                               @Param("endTime") String endTime);
}

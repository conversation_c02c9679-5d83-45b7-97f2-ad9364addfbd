package com.cty.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cty.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 城市实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("city")
public class City extends BaseEntity {
    
    /**
     * 城市编码
     */
    private String cityCode;
    
    /**
     * 城市名称
     */
    private String cityName;
    
    /**
     * 父级城市ID (0表示省份)
     */
    private Long parentId;
    
    /**
     * 城市级别 1-省份 2-城市 3-区县
     */
    private Integer level;
    
    /**
     * 城市全名 (如：北京市朝阳区)
     */
    private String fullName;
    
    /**
     * 城市简称
     */
    private String shortName;
    
    /**
     * 拼音
     */
    private String pinyin;
    
    /**
     * 拼音首字母
     */
    private String pinyinPrefix;
    
    /**
     * 经度
     */
    private BigDecimal longitude;
    
    /**
     * 纬度
     */
    private BigDecimal latitude;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;
    
    /**
     * 是否开通服务 0-未开通 1-已开通
     */
    private Integer serviceEnabled;
    
    /**
     * 服务半径(公里)
     */
    private Integer serviceRadius;
    
    /**
     * 城市描述
     */
    private String description;
    
    /**
     * 城市图标
     */
    private String icon;
    
    /**
     * 是否热门城市 0-否 1-是
     */
    private Integer isHot;
}

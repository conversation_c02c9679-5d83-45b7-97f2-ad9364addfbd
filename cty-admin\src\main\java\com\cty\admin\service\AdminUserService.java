package com.cty.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cty.admin.entity.AdminUser;

/**
 * 管理员用户服务接口
 */
public interface AdminUserService extends IService<AdminUser> {
    
    /**
     * 管理员登录
     */
    String login(String username, String password);
    
    /**
     * 根据用户名查询管理员
     */
    AdminUser getByUsername(String username);
    
    /**
     * 获取用户列表
     */
    Page<Object> getUserList(Integer pageNum, Integer pageSize, String keyword, Integer status);
    
    /**
     * 用户实名认证审核
     */
    boolean auditUser(Long userId, Integer status, String remark);
    
    /**
     * 获取订单列表
     */
    Page<Object> getOrderList(Integer pageNum, Integer pageSize, String orderNo, Integer status, String startTime, String endTime);
    
    /**
     * 获取统计数据
     */
    Object getStatistics(String type, String period);
    
    /**
     * 冻结/解冻用户
     */
    boolean freezeUser(Long userId, Integer status, String reason);
}

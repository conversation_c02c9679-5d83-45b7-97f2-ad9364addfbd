import type { CSSProperties } from 'vue';
/** converting camel-cased strings to be lowercase and link it with Separato */
export declare function toLowercaseSeparator(key: string): string;
export declare function getStyleStr(style: CSSProperties): string;
/** Returns the ratio of the device's physical pixel resolution to the css pixel resolution */
export declare function getPixelRatio(): number;
/** Whether to re-render the watermark */
export declare const reRendering: (mutation: MutationRecord, watermarkElement?: HTMLElement) => boolean;

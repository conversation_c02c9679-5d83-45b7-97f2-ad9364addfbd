package com.cty.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cty.admin.entity.City;

import java.util.List;

/**
 * 城市服务接口
 */
public interface CityService extends IService<City> {
    
    /**
     * 分页查询城市列表
     */
    Page<City> getCityList(Integer pageNum, Integer pageSize, String keyword, Integer level, Long parentId, Integer status);
    
    /**
     * 根据父级ID查询子城市
     */
    List<City> getChildCities(Long parentId);
    
    /**
     * 查询热门城市
     */
    List<City> getHotCities();
    
    /**
     * 查询已开通服务的城市
     */
    List<City> getServiceEnabledCities();
    
    /**
     * 添加城市
     */
    boolean addCity(City city);
    
    /**
     * 更新城市
     */
    boolean updateCity(City city);
    
    /**
     * 删除城市
     */
    boolean deleteCity(Long id);
    
    /**
     * 启用/禁用城市
     */
    boolean updateCityStatus(Long id, Integer status);
    
    /**
     * 开通/关闭城市服务
     */
    boolean updateServiceStatus(Long id, Integer serviceEnabled);
    
    /**
     * 设置热门城市
     */
    boolean updateHotStatus(Long id, Integer isHot);
    
    /**
     * 获取城市树形结构
     */
    List<City> getCityTree();

    /**
     * 根据城市编码查询城市
     */
    City getByCityCode(String cityCode);
}

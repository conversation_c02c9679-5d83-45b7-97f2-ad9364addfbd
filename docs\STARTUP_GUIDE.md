# 同城互助平台启动指南

## 🚀 快速启动步骤

### 1. 环境准备

#### 必需软件
- **JDK 8+** (推荐JDK 11或17)
- **Maven 3.6+**
- **MySQL 8.0+**
- **Redis 6.0+**
- **Nacos 2.0+**

#### 可选软件
- **RabbitMQ 3.8+** (消息队列)
- **Node.js 16+** (前端开发)

### 2. 基础服务启动

#### 启动MySQL
```bash
# 确保MySQL服务运行在3306端口
# 创建数据库
CREATE DATABASE city_help_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 启动Redis
```bash
# 确保Redis服务运行在6379端口
redis-server
```

#### 启动Nacos
```bash
# 下载Nacos 2.0+版本
# Windows
startup.cmd -m standalone

# Linux/Mac
sh startup.sh -m standalone
```

访问Nacos控制台: http://localhost:8848/nacos
- 用户名: nacos
- 密码: nacos

### 3. 数据库初始化

执行数据库脚本：
```bash
mysql -u root -p city_help_platform < sql/city_help_platform.sql
```

### 4. 微服务启动

#### 方法一：使用启动脚本
```bash
# Windows
scripts/start-services.bat

# Linux/Mac
chmod +x scripts/start-services.sh
./scripts/start-services.sh
```

#### 方法二：手动启动
按以下顺序启动各个微服务：

```bash
# 1. 启动网关服务
mvn spring-boot:run -pl cty-gateway

# 2. 启动用户服务
mvn spring-boot:run -pl cty-user

# 3. 启动订单服务
mvn spring-boot:run -pl cty-order

# 4. 启动支付服务
mvn spring-boot:run -pl cty-payment

# 5. 启动消息服务
mvn spring-boot:run -pl cty-message

# 6. 启动位置服务
mvn spring-boot:run -pl cty-location

# 7. 启动文件服务
mvn spring-boot:run -pl cty-file

# 8. 启动管理后台服务
mvn spring-boot:run -pl cty-admin
```

### 5. 前端启动

#### 管理后台前端
```bash
cd admin-web
npm install
npm run dev
```

访问地址: http://localhost:3000
- 用户名: admin
- 密码: admin123

## 🔧 常见问题解决

### 问题1: Nacos配置导入错误
```
No spring.config.import property has been defined
```

**解决方案**: 已在配置文件中添加了配置导入，确保配置如下：
```yaml
spring:
  config:
    import:
      - optional:nacos:服务名.yml
```

### 问题2: 数据库连接失败
**检查项**:
- MySQL服务是否启动
- 数据库是否创建
- 用户名密码是否正确
- 端口是否正确(3306)

### 问题3: Redis连接失败
**检查项**:
- Redis服务是否启动
- 端口是否正确(6379)
- 密码配置是否正确

### 问题4: Nacos注册失败
**检查项**:
- Nacos服务是否启动
- 端口是否正确(8848)
- 命名空间配置是否正确

### 问题5: 端口冲突
**解决方案**: 修改application.yml中的server.port配置

## 📋 服务端口列表

| 服务名称 | 端口 | 说明 |
|---------|------|------|
| 网关服务 | 8080 | API网关，统一入口 |
| 认证服务 | 8081 | 用户认证授权 |
| 用户服务 | 8082 | 用户信息管理 |
| 订单服务 | 8083 | 订单业务处理 |
| 支付服务 | 8084 | 支付相关功能 |
| 消息服务 | 8085 | 即时通讯 |
| 位置服务 | 8086 | 地理位置服务 |
| 文件服务 | 8087 | 文件上传下载 |
| 管理后台 | 8088 | 管理员功能 |

## 🌐 访问地址

### 后端服务
- **API网关**: http://localhost:8080
- **API文档**: http://localhost:8080/doc.html
- **Nacos控制台**: http://localhost:8848/nacos

### 前端应用
- **管理后台**: http://localhost:3000

## 🔍 健康检查

### 检查服务状态
```bash
# 检查服务注册情况
curl http://localhost:8848/nacos/v1/ns/instance/list?serviceName=cty-gateway

# 检查网关健康状态
curl http://localhost:8080/actuator/health

# 检查各个微服务健康状态
curl http://localhost:8082/actuator/health  # 用户服务
curl http://localhost:8083/actuator/health  # 订单服务
curl http://localhost:8088/actuator/health  # 管理后台
```

### 查看服务日志
```bash
# 查看特定服务日志
tail -f logs/gateway.log
tail -f logs/user.log
tail -f logs/admin.log
```

## 🛠️ 开发调试

### IDE配置
1. 导入Maven项目
2. 配置JDK版本
3. 启用Lombok插件
4. 配置代码格式化规则

### 调试模式启动
```bash
# 以调试模式启动特定服务
mvn spring-boot:run -pl cty-admin -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"
```

## 📞 技术支持

如遇到问题，请检查：
1. 日志文件中的错误信息
2. 服务注册状态
3. 数据库连接状态
4. 网络端口占用情况

更多问题请参考项目文档或联系开发团队。

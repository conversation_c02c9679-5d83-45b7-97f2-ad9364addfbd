<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="cty-payment" />
        <module name="cty-message" />
        <module name="cty-user" />
        <module name="cty-auth" />
        <module name="cty-order" />
        <module name="cty-file" />
        <module name="cty-admin" />
        <module name="cty-location" />
        <module name="cty-common" />
        <module name="cty-gateway" />
      </profile>
    </annotationProcessing>
  </component>
</project>
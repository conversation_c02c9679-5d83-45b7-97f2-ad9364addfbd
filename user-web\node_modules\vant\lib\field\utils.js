var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var stdin_exports = {};
__export(stdin_exports, {
  cutString: () => cutString,
  endComposing: () => endComposing,
  getRuleMessage: () => getRuleMessage,
  getStringLength: () => getStringLength,
  isEmptyValue: () => isEmptyValue,
  mapInputType: () => mapInputType,
  resizeTextarea: () => resizeTextarea,
  runRuleValidator: () => runRuleValidator,
  runSyncRule: () => runSyncRule,
  startComposing: () => startComposing
});
module.exports = __toCommonJS(stdin_exports);
var import_utils = require("../utils");
function isEmptyValue(value) {
  if (Array.isArray(value)) {
    return !value.length;
  }
  if (value === 0) {
    return false;
  }
  return !value;
}
function runSyncRule(value, rule) {
  if (isEmptyValue(value)) {
    if (rule.required) {
      return false;
    }
    if (rule.validateEmpty === false) {
      return true;
    }
  }
  if (rule.pattern && !rule.pattern.test(String(value))) {
    return false;
  }
  return true;
}
function runRuleValidator(value, rule) {
  return new Promise((resolve) => {
    const returnVal = rule.validator(value, rule);
    if ((0, import_utils.isPromise)(returnVal)) {
      returnVal.then(resolve);
      return;
    }
    resolve(returnVal);
  });
}
function getRuleMessage(value, rule) {
  const { message } = rule;
  if ((0, import_utils.isFunction)(message)) {
    return message(value, rule);
  }
  return message || "";
}
function startComposing({ target }) {
  target.composing = true;
}
function endComposing({ target }) {
  if (target.composing) {
    target.composing = false;
    target.dispatchEvent(new Event("input"));
  }
}
function resizeTextarea(input, autosize) {
  const scrollTop = (0, import_utils.getRootScrollTop)();
  input.style.height = "auto";
  let height = input.scrollHeight;
  if ((0, import_utils.isObject)(autosize)) {
    const { maxHeight, minHeight } = autosize;
    if (maxHeight !== void 0) {
      height = Math.min(height, maxHeight);
    }
    if (minHeight !== void 0) {
      height = Math.max(height, minHeight);
    }
  }
  if (height) {
    input.style.height = `${height}px`;
    (0, import_utils.setRootScrollTop)(scrollTop);
  }
}
function mapInputType(type, inputmode) {
  if (type === "number") {
    type = "text";
    inputmode != null ? inputmode : inputmode = "decimal";
  }
  if (type === "digit") {
    type = "tel";
    inputmode != null ? inputmode : inputmode = "numeric";
  }
  return { type, inputmode };
}
function getStringLength(str) {
  return [...str].length;
}
function cutString(str, maxlength) {
  return [...str].slice(0, maxlength).join("");
}

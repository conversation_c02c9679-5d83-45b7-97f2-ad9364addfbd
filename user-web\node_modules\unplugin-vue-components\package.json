{"name": "unplugin-vue-components", "type": "module", "version": "0.26.0", "packageManager": "pnpm@8.7.5", "description": "Components auto importing for Vue", "author": "antfu <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unplugin/unplugin-vue-components#readme", "repository": {"type": "git", "url": "https://github.com/unplugin/unplugin-vue-components.git"}, "bugs": "https://github.com/unplugin/unplugin-vue-components/issues", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./nuxt": {"import": "./dist/nuxt.js", "require": "./dist/nuxt.cjs"}, "./resolvers": {"import": "./dist/resolvers.js", "require": "./dist/resolvers.cjs"}, "./rollup": {"import": "./dist/rollup.js", "require": "./dist/rollup.cjs"}, "./types": {"import": "./dist/types.js", "require": "./dist/types.cjs"}, "./vite": {"import": "./dist/vite.js", "require": "./dist/vite.cjs"}, "./webpack": {"import": "./dist/webpack.js", "require": "./dist/webpack.cjs"}, "./rspack": {"import": "./dist/rspack.js", "require": "./dist/rspack.cjs"}, "./esbuild": {"import": "./dist/esbuild.js", "require": "./dist/esbuild.cjs"}, "./*": "./*"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "index.d.ts", "typesVersions": {"*": {"*": ["./dist/*"]}}, "files": ["dist"], "engines": {"node": ">=14"}, "scripts": {"build": "tsup", "dev": "tsup --watch src", "example:build": "npm -C examples/vite-vue3 run build", "example:dev": "npm -C examples/vite-vue3 run dev", "prepublishOnly": "npm run build", "lint": "eslint .", "release": "bumpp && npm publish", "test": "vitest", "test:update": "vitest --u"}, "peerDependencies": {"@babel/parser": "^7.15.8", "@nuxt/kit": "^3.2.2", "vue": "2 || 3"}, "peerDependenciesMeta": {"@babel/parser": {"optional": true}, "@nuxt/kit": {"optional": true}}, "dependencies": {"@antfu/utils": "^0.7.6", "@rollup/pluginutils": "^5.0.4", "chokidar": "^3.5.3", "debug": "^4.3.4", "fast-glob": "^3.3.1", "local-pkg": "^0.4.3", "magic-string": "^0.30.3", "minimatch": "^9.0.3", "resolve": "^1.22.4", "unplugin": "^1.4.0"}, "devDependencies": {"@antfu/eslint-config": "^0.42.0", "@babel/parser": "^7.22.16", "@babel/types": "^7.22.17", "@nuxt/kit": "^3.7.3", "@types/debug": "^4.1.8", "@types/minimatch": "^5.1.2", "@types/node": "^20.6.0", "@types/resolve": "^1.20.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "bumpp": "^9.2.0", "compare-versions": "^6.1.0", "element-plus": "^2.3.12", "eslint": "^8.49.0", "esno": "^0.17.0", "estree-walker": "^3.0.3", "pathe": "^1.1.1", "rollup": "^3.29.1", "tsup": "^7.2.0", "typescript": "^5.2.2", "vite": "^4.4.9", "vitest": "^0.34.4", "vue": "3.2.45"}}
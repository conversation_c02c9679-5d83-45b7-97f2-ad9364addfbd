import type { ComputedRef, SetupContext } from 'vue';
import type { FormItemContext } from 'element-plus/es/components/form';
import type { SliderEmits, SliderInitData, SliderProps } from '../slider';
export declare const useWatch: (props: SliderProps, initData: SliderInitData, minValue: ComputedRef<number>, maxValue: ComputedRef<number>, emit: SetupContext<SliderEmits>["emit"], elFormItem: FormItemContext) => void;

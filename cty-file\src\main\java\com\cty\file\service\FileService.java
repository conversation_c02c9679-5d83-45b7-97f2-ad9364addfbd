package com.cty.file.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文件服务接口
 */
public interface FileService {
    
    /**
     * 上传文件
     */
    String uploadFile(MultipartFile file, String folder);
    
    /**
     * 上传图片
     */
    String uploadImage(MultipartFile file);
    
    /**
     * 上传头像
     */
    String uploadAvatar(MultipartFile file);
    
    /**
     * 上传身份证照片
     */
    String uploadIdCard(MultipartFile file);
    
    /**
     * 删除文件
     */
    boolean deleteFile(String fileUrl);
    
    /**
     * 获取文件访问URL
     */
    String getFileUrl(String fileName);
    
    /**
     * 检查文件是否存在
     */
    boolean fileExists(String fileName);
    
    /**
     * 获取文件大小
     */
    long getFileSize(String fileName);
}

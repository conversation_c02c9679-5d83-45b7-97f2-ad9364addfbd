package com.cty.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cty.admin.entity.City;
import com.cty.admin.mapper.CityMapper;
import com.cty.admin.service.CityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 城市服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CityServiceImpl extends ServiceImpl<CityMapper, City> implements CityService {

    private final CityMapper cityMapper;

    @Override
    public Page<City> getCityList(Integer pageNum, Integer pageSize, String keyword, Integer level, Long parentId, Integer status) {
        Page<City> page = new Page<>(pageNum, pageSize);
        return cityMapper.selectCityPage(page, keyword, level, parentId, status);
    }

    @Override
    public List<City> getChildCities(Long parentId) {
        return cityMapper.selectByParentId(parentId);
    }

    @Override
    public List<City> getHotCities() {
        return cityMapper.selectHotCities();
    }

    @Override
    public List<City> getServiceEnabledCities() {
        return cityMapper.selectServiceEnabledCities();
    }

    @Override
    @Transactional
    public boolean addCity(City city) {
        city.setCreateTime(LocalDateTime.now());
        city.setUpdateTime(LocalDateTime.now());
        return save(city);
    }

    @Override
    @Transactional
    public boolean updateCity(City city) {
        city.setUpdateTime(LocalDateTime.now());
        return updateById(city);
    }

    @Override
    @Transactional
    public boolean deleteCity(Long id) {
        // 检查是否有子城市
        long childCount = count(new LambdaQueryWrapper<City>()
                .eq(City::getParentId, id)
                .eq(City::getDeleted, 0));
        
        if (childCount > 0) {
            throw new RuntimeException("该城市下还有子城市，无法删除");
        }
        
        return removeById(id);
    }

    @Override
    @Transactional
    public boolean updateCityStatus(Long id, Integer status) {
        City city = new City();
        city.setId(id);
        city.setStatus(status);
        city.setUpdateTime(LocalDateTime.now());
        return updateById(city);
    }

    @Override
    @Transactional
    public boolean updateServiceStatus(Long id, Integer serviceEnabled) {
        City city = new City();
        city.setId(id);
        city.setServiceEnabled(serviceEnabled);
        city.setUpdateTime(LocalDateTime.now());
        return updateById(city);
    }

    @Override
    @Transactional
    public boolean updateHotStatus(Long id, Integer isHot) {
        City city = new City();
        city.setId(id);
        city.setIsHot(isHot);
        city.setUpdateTime(LocalDateTime.now());
        return updateById(city);
    }

    @Override
    public List<City> getCityTree() {
        // 查询所有省份
        List<City> provinces = list(new LambdaQueryWrapper<City>()
                .eq(City::getLevel, 1)
                .eq(City::getStatus, 1)
                .eq(City::getDeleted, 0)
                .orderByAsc(City::getSort));
        
        // 为每个省份查询子城市
        for (City province : provinces) {
            List<City> cities = getChildCities(province.getId());
            // 这里可以设置children属性，如果City实体有的话
        }
        
        return provinces;
    }

    @Override
    public City getByCityCode(String cityCode) {
        return getOne(new LambdaQueryWrapper<City>()
                .eq(City::getCityCode, cityCode)
                .eq(City::getDeleted, 0));
    }
}

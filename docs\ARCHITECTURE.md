# 同城互助平台架构设计文档

## 🏗️ 整体架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        前端层                                │
├─────────────────────────────────────────────────────────────┤
│  用户端(Vue3+ElementPlus)  │  管理端(Vue3+ElementPlus)      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      API网关层                               │
├─────────────────────────────────────────────────────────────┤
│              Spring Cloud Gateway (8080)                   │
│        路由转发 │ 负载均衡 │ 限流熔断 │ 统一鉴权              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      微服务层                               │
├─────────────────────────────────────────────────────────────┤
│ 认证服务 │ 用户服务 │ 订单服务 │ 支付服务 │ 消息服务          │
│  (8081) │  (8082) │  (8083) │  (8084) │  (8085)           │
│─────────────────────────────────────────────────────────────│
│ 位置服务 │ 文件服务 │ 管理服务 │                            │
│  (8086) │  (8087) │  (8088) │                            │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      基础设施层                              │
├─────────────────────────────────────────────────────────────┤
│  Nacos  │  MySQL  │  Redis  │ RabbitMQ │  Seata  │  MinIO  │
│ (8848)  │ (3306)  │ (6379)  │  (5672)  │ (8091)  │ (9000)  │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 技术栈详解

### 后端技术栈
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| Spring Boot | 2.7.14 | 基础框架 | 微服务基础框架 |
| Spring Cloud | 2021.0.8 | 微服务治理 | 服务注册发现、配置管理 |
| Spring Cloud Alibaba | 2021.0.5.0 | 阿里云生态 | Nacos、Seata集成 |
| MyBatis Plus | 3.5.3.1 | ORM框架 | 数据库操作简化 |
| MySQL | 8.0+ | 关系数据库 | 主要数据存储 |
| Redis | 6.0+ | 缓存数据库 | 缓存、会话存储 |
| RabbitMQ | 3.8+ | 消息队列 | 异步消息处理 |
| Nacos | 2.0+ | 注册中心 | 服务注册发现、配置中心 |
| Seata | 1.6.1 | 分布式事务 | 保证数据一致性 |
| JWT | 0.9.1 | 身份认证 | 无状态身份验证 |

### 前端技术栈
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| Vue | 3.x | 前端框架 | 响应式前端框架 |
| Element Plus | 2.x | UI组件库 | 基于Vue3的组件库 |
| Vite | 4.x | 构建工具 | 快速构建和热更新 |
| Pinia | 2.x | 状态管理 | Vue3状态管理 |
| Vue Router | 4.x | 路由管理 | 前端路由 |
| Axios | 1.x | HTTP客户端 | API请求处理 |

## 📋 服务职责划分

### 🔗 cty-common (公共模块)
**职责**: 提供通用基础组件
- 统一响应格式和状态码
- 基础实体类和工具类
- 公共配置和常量定义

### 🌐 cty-gateway (API网关 - 8080)
**职责**: 系统统一入口
- 请求路由和负载均衡
- 统一鉴权和权限控制
- 限流熔断和监控

### 🔐 cty-auth (认证服务 - 8081)
**职责**: 身份认证和授权
- JWT Token管理
- 用户登录验证
- 权限控制

### 👤 cty-user (用户服务 - 8082)
**职责**: 用户信息管理
- 用户注册登录
- 个人信息管理
- 实名认证
- 技能和信用管理

### 📋 cty-order (订单服务 - 8083)
**职责**: 业务核心逻辑
- 互助需求管理
- 订单生命周期
- 智能匹配算法
- 评价反馈

### 💰 cty-payment (支付服务 - 8084)
**职责**: 资金管理
- 多种支付方式
- 钱包管理
- 担保交易
- 资金流水

### 💬 cty-message (消息服务 - 8085)
**职责**: 通讯功能
- 即时聊天
- 系统通知
- 消息推送
- WebSocket通讯

### 📍 cty-location (位置服务 - 8086)
**职责**: 地理位置处理
- 位置计算
- 地址解析
- 附近搜索
- 地图服务集成

### 📁 cty-file (文件服务 - 8087)
**职责**: 文件管理
- 文件上传下载
- 多种存储支持
- 图片处理
- 访问控制

### 🛠️ cty-admin (管理服务 - 8088)
**职责**: 平台管理
- 用户管理审核
- 内容审核
- 数据统计
- 系统配置

## 🔄 服务间通信

### 同步通信
- **OpenFeign**: 服务间REST API调用
- **负载均衡**: Spring Cloud LoadBalancer
- **熔断降级**: Sentinel (可选)

### 异步通信
- **消息队列**: RabbitMQ
- **事件驱动**: 基于消息的事件处理
- **解耦合**: 减少服务间直接依赖

### 数据一致性
- **分布式事务**: Seata AT模式
- **最终一致性**: 基于消息的最终一致性
- **补偿机制**: 业务补偿处理

## 🗄️ 数据存储设计

### 数据库分配
- **MySQL**: 主要业务数据存储
- **Redis**: 缓存、会话、分布式锁
- **文件存储**: MinIO/阿里云OSS

### 缓存策略
- **多级缓存**: 本地缓存 + Redis缓存
- **缓存更新**: 写入时更新、定时刷新
- **缓存穿透**: 布隆过滤器防护

### 数据库设计原则
- **垂直拆分**: 按业务领域拆分
- **读写分离**: 主从复制提升性能
- **分库分表**: 大表水平拆分

## 🛡️ 安全设计

### 认证授权
- **JWT Token**: 无状态身份验证
- **RBAC模型**: 基于角色的权限控制
- **接口权限**: 细粒度权限控制

### 数据安全
- **敏感数据加密**: 密码、身份证等
- **数据脱敏**: 日志和展示脱敏
- **HTTPS传输**: 数据传输加密

### 系统安全
- **防SQL注入**: 参数化查询
- **XSS防护**: 输入输出过滤
- **CSRF防护**: Token验证

## 📊 监控运维

### 服务监控
- **健康检查**: Spring Boot Actuator
- **性能监控**: Micrometer + Prometheus
- **链路追踪**: SkyWalking/Zipkin

### 日志管理
- **统一日志**: ELK Stack
- **日志级别**: 分环境配置
- **审计日志**: 关键操作记录

### 部署运维
- **容器化**: Docker部署
- **编排管理**: Kubernetes
- **CI/CD**: Jenkins/GitLab CI

## 🚀 性能优化

### 缓存优化
- **热点数据缓存**: Redis缓存热点数据
- **查询结果缓存**: 复杂查询结果缓存
- **页面缓存**: 静态页面缓存

### 数据库优化
- **索引优化**: 合理创建索引
- **查询优化**: SQL语句优化
- **连接池**: 数据库连接池配置

### 系统优化
- **异步处理**: 耗时操作异步化
- **批量处理**: 批量操作提升效率
- **资源池化**: 连接池、线程池优化

## 📈 扩展性设计

### 水平扩展
- **无状态设计**: 服务无状态便于扩展
- **负载均衡**: 多实例负载分担
- **数据分片**: 数据水平分片

### 垂直扩展
- **服务拆分**: 按业务垂直拆分
- **资源隔离**: 不同服务资源隔离
- **专用优化**: 针对性能优化

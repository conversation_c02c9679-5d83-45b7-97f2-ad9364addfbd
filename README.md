# 同城互助平台

## 项目简介
同城互助平台是一个基于Spring Cloud微服务架构的互助服务平台，旨在为同城用户提供便民互助服务。

## 技术架构

### 后端技术栈
- **微服务框架**: Spring Cloud 2021.0.8
- **服务注册发现**: Nacos
- **配置中心**: Nacos Config
- **API网关**: Spring Cloud Gateway
- **负载均衡**: Spring Cloud LoadBalancer
- **服务调用**: OpenFeign
- **熔断降级**: Sentinel
- **分布式事务**: Seata
- **消息队列**: RabbitMQ
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis Plus
- **缓存**: Redis
- **文档**: Knife4j (Swagger3)

### 前端技术栈
- **框架**: Vue 3 + Composition API
- **UI组件**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios

## 项目结构

```
city-help-platform/
├── cty-common/              # 公共模块
│   ├── src/main/java/com/cty/common/
│   │   ├── result/          # 统一响应结果
│   │   ├── entity/          # 基础实体类
│   │   └── dto/             # 数据传输对象
│   └── pom.xml
├── cty-gateway/             # 网关服务 (8080)
│   ├── src/main/java/com/cty/gateway/
│   └── src/main/resources/application.yml
├── cty-auth/                # 认证授权服务 (8081)
│   ├── src/main/java/com/cty/auth/
│   └── src/main/resources/application.yml
├── cty-user/                # 用户服务 (8082)
│   ├── src/main/java/com/cty/user/
│   │   ├── entity/          # 用户实体
│   │   ├── mapper/          # 数据访问层
│   │   ├── service/         # 业务逻辑层
│   │   └── controller/      # 控制层
│   └── src/main/resources/application.yml
├── cty-order/               # 订单服务 (8083)
│   ├── src/main/java/com/cty/order/
│   └── src/main/resources/application.yml
├── cty-payment/             # 支付服务 (8084)
│   ├── src/main/java/com/cty/payment/
│   └── src/main/resources/application.yml
├── cty-message/             # 消息服务 (8085)
│   ├── src/main/java/com/cty/message/
│   │   ├── entity/          # 消息实体
│   │   ├── config/          # WebSocket配置
│   │   └── controller/      # 消息控制器
│   └── src/main/resources/application.yml
├── cty-location/            # 位置服务 (8086)
│   ├── src/main/java/com/cty/location/
│   │   └── service/         # 位置计算服务
│   └── src/main/resources/application.yml
├── cty-file/                # 文件服务 (8087)
│   ├── src/main/java/com/cty/file/
│   │   ├── service/         # 文件处理服务
│   │   └── controller/      # 文件上传控制器
│   └── src/main/resources/application.yml
├── cty-admin/               # 管理后台服务 (8088)
│   ├── src/main/java/com/cty/admin/
│   │   ├── entity/          # 管理员实体
│   │   └── controller/      # 管理接口
│   └── src/main/resources/application.yml
├── sql/                     # 数据库脚本
│   └── city_help_platform.sql
├── README.md                # 项目说明文档
└── pom.xml                  # 父工程POM
```

## 核心功能模块

### 1. 用户系统
- 用户注册/登录
- 实名认证
- 用户资料管理
- 信用评级系统
- 地理位置管理

### 2. 互助需求
- 需求发布
- 需求分类管理
- 需求搜索匹配
- 需求状态管理

### 3. 订单管理
- 订单创建
- 订单状态流转
- 订单支付
- 订单评价

### 4. 支付系统
- 多种支付方式
- 钱包管理
- 支付记录
- 资金流水

### 5. 消息通讯
- 即时聊天
- 系统通知
- 消息推送

### 6. 社区功能
- 动态发布
- 评论点赞
- 用户关注

### 7. 管理后台
- 用户管理
- 内容审核
- 数据统计
- 系统配置

## 数据库设计

### 核心表结构
- `user` - 用户表
- `user_skill` - 用户技能表
- `help_category` - 互助需求分类表
- `help_request` - 互助需求表
- `order_info` - 订单表
- `payment_record` - 支付记录表
- `user_wallet` - 用户钱包表
- `message` - 消息表
- `evaluation` - 评价表
- `community_post` - 社区动态表

## 快速开始

### 环境要求
- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- RabbitMQ 3.8+
- Nacos 2.0+

### 启动步骤

1. **启动基础服务**
   ```bash
   # 启动MySQL
   # 启动Redis
   # 启动RabbitMQ
   # 启动Nacos
   ```

2. **初始化数据库**
   ```bash
   # 执行sql/city_help_platform.sql
   ```

3. **启动微服务**
   ```bash
   # 按顺序启动各个微服务
   mvn spring-boot:run -pl cty-gateway
   mvn spring-boot:run -pl cty-auth
   mvn spring-boot:run -pl cty-user
   mvn spring-boot:run -pl cty-order
   mvn spring-boot:run -pl cty-payment
   # ... 其他服务
   ```

## API文档
启动服务后访问: http://localhost:8080/doc.html

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 统一响应格式

### 数据库规范
- 表名使用下划线命名
- 字段名使用下划线命名
- 必须有主键、创建时间、更新时间
- 使用逻辑删除

### 接口规范
- RESTful API设计
- 统一返回格式
- 完善的接口文档
- 参数校验

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t cty-gateway .

# 运行容器
docker run -d -p 8080:8080 cty-gateway
```

### K8s部署
```bash
# 应用配置
kubectl apply -f k8s/
```

## 联系方式
- 项目地址: https://github.com/your-repo/city-help-platform
- 技术交流群: xxx

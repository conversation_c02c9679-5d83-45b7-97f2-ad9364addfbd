package com.cty.admin.config;

import com.cty.admin.entity.AdminUser;
import com.cty.admin.service.AdminUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 数据初始化器
 * 在应用启动时创建默认管理员账户
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {

    private final AdminUserService adminUserService;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        initDefaultAdmin();
    }

    /**
     * 初始化默认管理员账户
     */
    private void initDefaultAdmin() {
        try {
            // 检查是否已存在admin用户
            AdminUser existingAdmin = adminUserService.getByUsername("admin");
            if (existingAdmin != null) {
                log.info("默认管理员账户已存在，跳过初始化");
                return;
            }

            // 创建默认管理员账户
            AdminUser admin = new AdminUser();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setRealName("系统管理员");
            admin.setPhone("13800138000");
            admin.setEmail("<EMAIL>");
            admin.setRoleId(1L);
            admin.setStatus(1);
            admin.setCreateTime(LocalDateTime.now());
            admin.setUpdateTime(LocalDateTime.now());

            boolean saved = adminUserService.save(admin);
            if (saved) {
                log.info("默认管理员账户创建成功: admin/admin123");
            } else {
                log.error("默认管理员账户创建失败");
            }
        } catch (Exception e) {
            log.error("初始化默认管理员账户时发生错误", e);
        }
    }
}

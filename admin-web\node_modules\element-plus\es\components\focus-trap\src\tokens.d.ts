import type { InjectionKey, Ref } from 'vue';
export declare const FOCUS_AFTER_TRAPPED = "focus-trap.focus-after-trapped";
export declare const FOCUS_AFTER_RELEASED = "focus-trap.focus-after-released";
export declare const FOCUSOUT_PREVENTED = "focus-trap.focusout-prevented";
export declare const FOCUS_AFTER_TRAPPED_OPTS: EventInit;
export declare const FOCUSOUT_PREVENTED_OPTS: EventInit;
export declare const ON_TRAP_FOCUS_EVT = "focusAfterTrapped";
export declare const ON_RELEASE_FOCUS_EVT = "focusAfterReleased";
export type FocusTrapInjectionContext = {
    focusTrapRef: Ref<HTMLElement | undefined>;
    onKeydown: (e: KeyboardEvent) => void;
};
export declare const FOCUS_TRAP_INJECTION_KEY: InjectionKey<FocusTrapInjectionContext>;

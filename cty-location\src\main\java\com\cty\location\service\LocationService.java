package com.cty.location.service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 位置服务接口
 */
public interface LocationService {
    
    /**
     * 计算两点之间的距离（单位：米）
     */
    double calculateDistance(BigDecimal lat1, BigDecimal lng1, BigDecimal lat2, BigDecimal lng2);
    
    /**
     * 根据地址获取经纬度
     */
    LocationInfo getLocationByAddress(String address);
    
    /**
     * 根据经纬度获取地址信息
     */
    AddressInfo getAddressByLocation(BigDecimal latitude, BigDecimal longitude);
    
    /**
     * 获取附近的用户
     */
    List<Long> getNearbyUsers(BigDecimal latitude, BigDecimal longitude, double radius);
    
    /**
     * 获取附近的需求
     */
    List<Long> getNearbyRequests(BigDecimal latitude, BigDecimal longitude, double radius);
    
    /**
     * 位置信息
     */
    class LocationInfo {
        private BigDecimal latitude;
        private BigDecimal longitude;
        
        // getters and setters
        public BigDecimal getLatitude() { return latitude; }
        public void setLatitude(BigDecimal latitude) { this.latitude = latitude; }
        public BigDecimal getLongitude() { return longitude; }
        public void setLongitude(BigDecimal longitude) { this.longitude = longitude; }
    }
    
    /**
     * 地址信息
     */
    class AddressInfo {
        private String province;
        private String city;
        private String district;
        private String address;
        
        // getters and setters
        public String getProvince() { return province; }
        public void setProvince(String province) { this.province = province; }
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        public String getDistrict() { return district; }
        public void setDistrict(String district) { this.district = district; }
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
    }
}

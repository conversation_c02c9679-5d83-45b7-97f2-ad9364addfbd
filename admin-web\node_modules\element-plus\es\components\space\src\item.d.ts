import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
export declare const spaceItemProps: {
    readonly prefixCls: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
};
export type SpaceItemProps = ExtractPropTypes<typeof spaceItemProps>;
export type SpaceItemPropsPublic = __ExtractPublicPropTypes<typeof spaceItemProps>;
declare const SpaceItem: import("vue").DefineComponent<{
    readonly prefixCls: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<ExtractPropTypes<{
    readonly prefixCls: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}>>, {}>;
export type SpaceItemInstance = InstanceType<typeof SpaceItem> & unknown;
export default SpaceItem;

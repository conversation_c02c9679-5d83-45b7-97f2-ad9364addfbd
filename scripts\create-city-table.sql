-- 创建城市表的脚本
USE city_help_platform;

-- 删除已存在的city表（如果存在）
DROP TABLE IF EXISTS city;

-- 创建城市表
CREATE TABLE city (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    city_code VARCHAR(20) NOT NULL UNIQUE COMMENT '城市编码',
    city_name VARCHAR(100) NOT NULL COMMENT '城市名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父级城市ID (0表示省份)',
    level TINYINT NOT NULL COMMENT '城市级别 1-省份 2-城市 3-区县',
    full_name VARCHAR(200) COMMENT '城市全名',
    short_name VARCHAR(50) COMMENT '城市简称',
    pinyin VARCHAR(200) COMMENT '拼音',
    pinyin_prefix VARCHAR(50) COMMENT '拼音首字母',
    longitude DECIMAL(10,6) COMMENT '经度',
    latitude DECIMAL(10,6) COMMENT '纬度',
    sort INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
    service_enabled TINYINT DEFAULT 0 COMMENT '是否开通服务 0-未开通 1-已开通',
    service_radius INT DEFAULT 10 COMMENT '服务半径(公里)',
    description VARCHAR(500) COMMENT '城市描述',
    icon VARCHAR(255) COMMENT '城市图标',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门城市 0-否 1-是',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除 0-未删除 1-已删除',
    version INT DEFAULT 1 COMMENT '版本号',
    
    -- 添加索引
    INDEX idx_city_code (city_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_service_enabled (service_enabled),
    INDEX idx_is_hot (is_hot),
    INDEX idx_deleted (deleted)
) COMMENT '城市表' ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入省份数据
INSERT INTO city (city_code, city_name, parent_id, level, full_name, short_name, pinyin, pinyin_prefix, sort, status, service_enabled, is_hot) VALUES 
('110000', '北京市', 0, 1, '北京市', '北京', 'beijing', 'BJ', 1, 1, 1, 1),
('120000', '天津市', 0, 1, '天津市', '天津', 'tianjin', 'TJ', 2, 1, 1, 1),
('310000', '上海市', 0, 1, '上海市', '上海', 'shanghai', 'SH', 3, 1, 1, 1),
('440000', '广东省', 0, 1, '广东省', '广东', 'guangdong', 'GD', 4, 1, 1, 1),
('320000', '江苏省', 0, 1, '江苏省', '江苏', 'jiangsu', 'JS', 5, 1, 1, 0),
('330000', '浙江省', 0, 1, '浙江省', '浙江', 'zhejiang', 'ZJ', 6, 1, 1, 0),
('510000', '四川省', 0, 1, '四川省', '四川', 'sichuan', 'SC', 7, 1, 1, 0),
('420000', '湖北省', 0, 1, '湖北省', '湖北', 'hubei', 'HB', 8, 1, 0, 0);

-- 插入城市数据（以北京、上海、广东为例）
INSERT INTO city (city_code, city_name, parent_id, level, full_name, short_name, pinyin, pinyin_prefix, longitude, latitude, sort, status, service_enabled, is_hot) VALUES 
-- 北京市区县
('110100', '北京城区', 1, 2, '北京市城区', '北京城区', 'beijingchengqu', 'BJCQ', 116.407526, 39.904030, 1, 1, 1, 1),
('110101', '东城区', 1, 3, '北京市东城区', '东城', 'dongchengqu', 'DCQ', 116.418757, 39.917544, 1, 1, 1, 0),
('110102', '西城区', 1, 3, '北京市西城区', '西城', 'xichengqu', 'XCQ', 116.366794, 39.915309, 2, 1, 1, 0),
('110105', '朝阳区', 1, 3, '北京市朝阳区', '朝阳', 'chaoyangqu', 'CYQ', 116.443108, 39.921489, 3, 1, 1, 1),
('110106', '丰台区', 1, 3, '北京市丰台区', '丰台', 'fengtaiqu', 'FTQ', 116.286968, 39.858427, 4, 1, 1, 0),
('110107', '石景山区', 1, 3, '北京市石景山区', '石景山', 'shijingshanqu', 'SJSQ', 116.222982, 39.906611, 5, 1, 1, 0),

-- 上海市区县
('310100', '上海城区', 3, 2, '上海市城区', '上海城区', 'shanghaichengqu', 'SHCQ', 121.473701, 31.230416, 1, 1, 1, 1),
('310101', '黄浦区', 3, 3, '上海市黄浦区', '黄浦', 'huangpuqu', 'HPQ', 121.490317, 31.222771, 1, 1, 1, 1),
('310104', '徐汇区', 3, 3, '上海市徐汇区', '徐汇', 'xuhuiqu', 'XHQ', 121.436525, 31.188523, 2, 1, 1, 0),
('310105', '长宁区', 3, 3, '上海市长宁区', '长宁', 'changningqu', 'CNQ', 121.424624, 31.220367, 3, 1, 1, 0),
('310106', '静安区', 3, 3, '上海市静安区', '静安', 'jinganqu', 'JAQ', 121.448224, 31.229003, 4, 1, 1, 0),
('310107', '普陀区', 3, 3, '上海市普陀区', '普陀', 'putuoqu', 'PTQ', 121.395514, 31.249162, 5, 1, 1, 0),

-- 广东省主要城市
('440100', '广州市', 4, 2, '广东省广州市', '广州', 'guangzhoushi', 'GZS', 113.264434, 23.129162, 1, 1, 1, 1),
('440300', '深圳市', 4, 2, '广东省深圳市', '深圳', 'shenzhenshi', 'SZS', 114.085947, 22.547, 2, 1, 1, 1),
('440600', '佛山市', 4, 2, '广东省佛山市', '佛山', 'foshanshi', 'FSS', 113.122717, 23.028762, 3, 1, 1, 0),
('441900', '东莞市', 4, 2, '广东省东莞市', '东莞', 'dongguanshi', 'DGS', 113.746262, 23.046237, 4, 1, 0, 0),

-- 江苏省主要城市
('320100', '南京市', 5, 2, '江苏省南京市', '南京', 'nanjingshi', 'NJS', 118.767413, 32.041544, 1, 1, 1, 1),
('320500', '苏州市', 5, 2, '江苏省苏州市', '苏州', 'suzhoushi', 'SZS', 120.619585, 31.299379, 2, 1, 1, 0),
('320200', '无锡市', 5, 2, '江苏省无锡市', '无锡', 'wuxishi', 'WXS', 120.301663, 31.574729, 3, 1, 0, 0),

-- 浙江省主要城市
('330100', '杭州市', 6, 2, '浙江省杭州市', '杭州', 'hangzhoushi', 'HZS', 120.153576, 30.287459, 1, 1, 1, 1),
('330200', '宁波市', 6, 2, '浙江省宁波市', '宁波', 'ningboshi', 'NBS', 121.549792, 29.868388, 2, 1, 1, 0),
('330300', '温州市', 6, 2, '浙江省温州市', '温州', 'wenzhoushi', 'WZS', 120.672111, 28.000575, 3, 1, 0, 0),

-- 四川省主要城市
('510100', '成都市', 7, 2, '四川省成都市', '成都', 'chengdushi', 'CDS', 104.065735, 30.659462, 1, 1, 1, 1),

-- 湖北省主要城市
('420100', '武汉市', 8, 2, '湖北省武汉市', '武汉', 'wuhanshi', 'WHS', 114.298572, 30.584355, 1, 1, 0, 0);

-- 查看创建结果
SELECT '城市表创建完成' as message;
SELECT COUNT(*) as total_cities FROM city WHERE deleted = 0;
SELECT level, COUNT(*) as count FROM city WHERE deleted = 0 GROUP BY level ORDER BY level;
SELECT COUNT(*) as service_enabled_count FROM city WHERE deleted = 0 AND service_enabled = 1;
SELECT COUNT(*) as hot_cities_count FROM city WHERE deleted = 0 AND is_hot = 1;

-- 显示热门城市
SELECT city_name, level, service_enabled FROM city WHERE deleted = 0 AND is_hot = 1 ORDER BY sort;

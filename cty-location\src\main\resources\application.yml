server:
  port: 8086

spring:
  application:
    name: cty-location
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
      config:
        server-addr: localhost:8848
        namespace: dev
        file-extension: yml
        group: DEFAULT_GROUP

  redis:
    host: localhost
    port: 6379
    password: 
    database: 5
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# 地图服务配置
map:
  # 高德地图API配置
  amap:
    key: your-amap-key
    url: https://restapi.amap.com
  # 百度地图API配置  
  baidu:
    ak: your-baidu-ak
    url: https://api.map.baidu.com

logging:
  level:
    com.cty.location: debug

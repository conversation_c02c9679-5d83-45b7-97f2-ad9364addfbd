<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cty.admin.mapper.AdminUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cty.admin.entity.AdminUser">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="real_name" property="realName" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="avatar" property="avatar" />
        <result column="role_id" property="roleId" />
        <result column="status" property="status" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="last_login_ip" property="lastLoginIp" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, password, real_name, phone, email, avatar, role_id, status, 
        last_login_time, last_login_ip, create_time, update_time, create_by, update_by, deleted, version
    </sql>

</mapper>

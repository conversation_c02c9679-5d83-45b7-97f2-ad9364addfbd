package com.cty.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cty.admin.entity.City;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 城市Mapper接口
 */
@Mapper
public interface CityMapper extends BaseMapper<City> {
    
    /**
     * 分页查询城市列表
     */
    Page<City> selectCityPage(Page<City> page, 
                             @Param("keyword") String keyword,
                             @Param("level") Integer level,
                             @Param("parentId") Long parentId,
                             @Param("status") Integer status);
    
    /**
     * 根据父级ID查询子城市
     */
    List<City> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询热门城市
     */
    List<City> selectHotCities();
    
    /**
     * 查询已开通服务的城市
     */
    List<City> selectServiceEnabledCities();
}

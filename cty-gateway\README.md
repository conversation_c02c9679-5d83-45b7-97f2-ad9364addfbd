# CTY-Gateway API网关服务

## 📋 模块概述
API网关是整个微服务架构的统一入口，负责请求路由、负载均衡、安全控制等功能。

## 🎯 核心功能

### 1. 请求路由
- **动态路由**: 基于服务发现的动态路由配置
- **路径重写**: 统一API路径格式
- **负载均衡**: 自动分发请求到健康的服务实例

### 2. 安全控制
- **跨域处理**: 统一CORS配置
- **请求限流**: 防止服务过载
- **黑白名单**: IP访问控制
- **Token验证**: JWT令牌验证

### 3. 监控和日志
- **请求日志**: 记录所有API调用
- **性能监控**: 响应时间统计
- **错误追踪**: 异常信息收集

## 🌐 路由配置

### 服务路由表
| 服务名称 | 路由前缀 | 目标端口 | 说明 |
|---------|---------|---------|------|
| 用户服务 | /api/user/** | 8082 | 用户相关接口 |
| 订单服务 | /api/order/** | 8083 | 订单相关接口 |
| 支付服务 | /api/payment/** | 8084 | 支付相关接口 |
| 消息服务 | /api/message/** | 8085 | 消息相关接口 |
| 位置服务 | /api/location/** | 8086 | 位置相关接口 |
| 文件服务 | /api/file/** | 8087 | 文件相关接口 |
| 管理后台 | /api/admin/** | 8088 | 管理相关接口 |

### 路由规则
```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: cty-user
          uri: lb://cty-user
          predicates:
            - Path=/api/user/**
          filters:
            - StripPrefix=2
```

## 🔧 技术特性

### 1. 服务发现
- 集成Nacos服务注册中心
- 自动发现服务实例
- 健康检查和故障转移

### 2. 负载均衡
- 轮询算法
- 权重分配
- 故障实例自动剔除

### 3. 熔断降级
- 集成Sentinel
- 自动熔断保护
- 降级策略配置

## 📊 监控指标
- 请求总数
- 响应时间
- 错误率
- 并发连接数
- 服务健康状态

## 🚀 启动配置
- **端口**: 8080
- **注册中心**: Nacos (localhost:8848)
- **配置中心**: Nacos Config
- **缓存**: Redis

## 🔍 API文档
访问地址: http://localhost:8080/doc.html

## 🛡️ 安全策略
1. **请求验证**: 参数校验和格式检查
2. **访问控制**: 基于角色的权限控制
3. **限流保护**: 防止恶意请求
4. **日志审计**: 完整的访问日志记录

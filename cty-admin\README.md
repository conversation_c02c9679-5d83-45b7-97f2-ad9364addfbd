# CTY-Admin 管理后台服务

## 📋 模块概述
管理后台服务为平台运营人员提供完整的管理功能，包括用户管理、内容审核、数据统计、系统配置等。

## 🎯 核心功能

### 1. 用户管理
- **用户列表**: 查看所有注册用户信息
- **用户详情**: 查看用户详细资料和行为记录
- **实名审核**: 审核用户实名认证申请
- **账户控制**: 冻结/解冻用户账户
- **信用管理**: 调整用户信用分数

### 2. 内容审核
- **需求审核**: 审核用户发布的互助需求
- **动态审核**: 审核社区动态内容
- **评论审核**: 审核用户评论内容
- **举报处理**: 处理用户举报投诉
- **敏感词管理**: 维护敏感词库

### 3. 订单监控
- **订单列表**: 查看所有订单信息
- **订单详情**: 查看订单完整流程
- **异常订单**: 监控异常订单情况
- **纠纷处理**: 处理订单纠纷
- **退款管理**: 处理退款申请

### 4. 数据统计
- **用户统计**: 用户注册、活跃度统计
- **订单统计**: 订单量、成交额统计
- **收入统计**: 平台收入和分成统计
- **地区分析**: 各地区业务分布
- **趋势分析**: 业务发展趋势

### 5. 系统管理
- **管理员管理**: 管理员账户和权限管理
- **角色权限**: 角色和权限配置
- **系统配置**: 平台参数配置
- **公告管理**: 系统公告发布
- **日志管理**: 系统操作日志

## 👥 权限管理体系

### 角色定义
- **超级管理员**: 拥有所有权限
- **运营管理员**: 负责日常运营管理
- **客服管理员**: 负责客服和投诉处理
- **财务管理员**: 负责财务相关功能
- **审核管理员**: 负责内容审核

### 权限分类
- **用户管理权限**: 用户查看、编辑、冻结
- **内容管理权限**: 内容审核、删除、推荐
- **订单管理权限**: 订单查看、处理、退款
- **财务管理权限**: 财务数据查看、结算
- **系统管理权限**: 系统配置、管理员管理

## 📊 数据模型

### 管理员用户
```java
public class AdminUser extends BaseEntity {
    private String username;         // 用户名
    private String password;         // 密码
    private String realName;         // 真实姓名
    private String phone;            // 手机号
    private String email;            // 邮箱
    private String avatar;           // 头像
    private Long roleId;             // 角色ID
    private Integer status;          // 状态
    private LocalDateTime lastLoginTime; // 最后登录时间
    private String lastLoginIp;      // 最后登录IP
    // ... 其他字段
}
```

### 角色权限
```java
public class AdminRole extends BaseEntity {
    private String name;             // 角色名称
    private String code;             // 角色编码
    private String description;      // 角色描述
    private Integer status;          // 状态
    // ... 其他字段
}

public class AdminPermission extends BaseEntity {
    private Long parentId;           // 父权限ID
    private String name;             // 权限名称
    private String code;             // 权限编码
    private Integer type;            // 权限类型
    private String path;             // 路径
    private String icon;             // 图标
    private Integer sortOrder;       // 排序
    // ... 其他字段
}
```

## 🔧 核心接口

### 1. 管理员登录
```http
POST /admin/user/login
Content-Type: application/x-www-form-urlencoded

username=admin&password=admin123
```

### 2. 获取用户列表
```http
GET /admin/user/list?pageNum=1&pageSize=10&keyword=张三&status=1
```

### 3. 用户实名审核
```http
POST /admin/user/audit
Content-Type: application/json

{
    "userId": 123,
    "status": 2,
    "remark": "审核通过"
}
```

### 4. 获取订单列表
```http
GET /admin/orders?pageNum=1&pageSize=10&status=1&startTime=2023-01-01&endTime=2023-12-31
```

### 5. 获取统计数据
```http
GET /admin/statistics?type=user&period=month
```

## 📈 数据统计功能

### 用户统计
```java
/**
 * 用户统计数据
 */
public class UserStatistics {
    private Long totalUsers;         // 总用户数
    private Long newUsers;           // 新增用户数
    private Long activeUsers;        // 活跃用户数
    private Long verifiedUsers;      // 实名用户数
    private Double verificationRate; // 实名率
    private Map<String, Long> cityDistribution; // 城市分布
    // ... 其他统计字段
}
```

### 订单统计
```java
/**
 * 订单统计数据
 */
public class OrderStatistics {
    private Long totalOrders;        // 总订单数
    private Long completedOrders;    // 完成订单数
    private BigDecimal totalAmount;  // 总交易额
    private Double completionRate;   // 完成率
    private Map<String, Long> categoryDistribution; // 分类分布
    private List<DailyOrderData> dailyData; // 每日数据
    // ... 其他统计字段
}
```

### 收入统计
```java
/**
 * 收入统计数据
 */
public class RevenueStatistics {
    private BigDecimal totalRevenue;     // 总收入
    private BigDecimal platformRevenue;  // 平台收入
    private BigDecimal userRevenue;      // 用户收入
    private Double platformRate;         // 平台抽成比例
    private Map<String, BigDecimal> monthlyRevenue; // 月度收入
    // ... 其他统计字段
}
```

## 🛠️ 审核工作流

### 实名认证审核流程
1. 用户提交认证资料
2. 系统自动初审
3. 人工审核确认
4. 审核结果通知
5. 记录审核日志

### 内容审核流程
1. 用户发布内容
2. 敏感词自动检测
3. 人工审核确认
4. 审核结果处理
5. 用户通知反馈

## 🔍 监控告警

### 业务监控
- 用户注册异常
- 订单异常波动
- 支付失败率
- 投诉举报量

### 系统监控
- 服务健康状态
- 接口响应时间
- 错误率统计
- 资源使用情况

## 📋 操作日志

### 日志记录
```java
public class OperationLog {
    private Long adminId;            // 操作人ID
    private String adminName;        // 操作人姓名
    private String operation;        // 操作类型
    private String module;           // 操作模块
    private String description;      // 操作描述
    private String requestUrl;       // 请求URL
    private String requestMethod;    // 请求方法
    private String requestParams;    // 请求参数
    private String responseResult;   // 响应结果
    private String clientIp;         // 客户端IP
    private LocalDateTime operationTime; // 操作时间
    // ... 其他字段
}
```

### 敏感操作记录
- 用户账户冻结/解冻
- 订单强制退款
- 系统配置修改
- 权限变更操作

## 🔐 安全机制

### 1. 访问控制
- 基于角色的权限控制
- 接口权限验证
- 操作权限检查
- 数据权限隔离

### 2. 安全审计
- 登录日志记录
- 操作行为审计
- 异常访问检测
- 安全事件告警

### 3. 数据保护
- 敏感数据脱敏
- 数据访问日志
- 数据导出控制
- 备份恢复机制

## 🚀 技术特性
- **端口**: 8088
- **安全框架**: Spring Security
- **权限控制**: RBAC模型
- **数据库**: MySQL (city_help_platform)
- **缓存**: Redis (database: 7)
- **前端框架**: Vue3 + Element Plus Admin

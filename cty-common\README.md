# CTY-Common 公共模块

## 📋 模块概述
公共模块是整个同城互助平台的基础组件库，为所有微服务提供统一的基础功能和工具类。

## 🎯 核心功能

### 1. 统一响应格式
- **Result类**: 标准化API响应格式
- **ResultCode枚举**: 统一状态码定义
- **支持泛型**: 灵活的数据类型支持

### 2. 基础实体类
- **BaseEntity**: 包含通用字段的基础实体
  - id: 主键ID (雪花算法)
  - createTime: 创建时间
  - updateTime: 更新时间
  - createBy: 创建人
  - updateBy: 更新人
  - deleted: 逻辑删除标识
  - version: 版本号 (乐观锁)

### 3. 数据传输对象
- **PageQuery**: 分页查询基类
  - 页码和页大小参数
  - 排序字段和方向
  - 参数校验注解

### 4. 工具类和常量
- 日期时间工具
- 字符串处理工具
- 加密解密工具
- 常量定义

## 📦 主要组件

```
cty-common/
├── result/              # 响应结果相关
│   ├── Result.java      # 统一响应格式
│   └── ResultCode.java  # 状态码枚举
├── entity/              # 基础实体
│   └── BaseEntity.java  # 基础实体类
├── dto/                 # 数据传输对象
│   └── PageQuery.java   # 分页查询基类
├── utils/               # 工具类
├── constants/           # 常量定义
└── config/              # 配置类
```

## 🔧 使用方式

### 1. 继承基础实体
```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user")
public class User extends BaseEntity {
    private String username;
    private String nickname;
    // ... 其他字段
}
```

### 2. 使用统一响应
```java
@RestController
public class UserController {
    
    @GetMapping("/user/{id}")
    public Result<User> getUser(@PathVariable Long id) {
        User user = userService.getById(id);
        return Result.success(user);
    }
}
```

### 3. 分页查询
```java
@Data
public class UserQuery extends PageQuery {
    private String username;
    private String city;
}
```

## 📋 依赖说明
- Spring Boot Web
- Spring Boot Validation
- MyBatis Plus
- MySQL Connector
- Redis
- JWT
- FastJSON
- Hutool
- Knife4j
- Lombok

## 🎯 设计原则
1. **统一性**: 提供统一的基础组件
2. **复用性**: 减少重复代码
3. **扩展性**: 支持业务扩展
4. **规范性**: 统一开发规范

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cty.admin.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cty.admin.entity.User">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="nickname" property="nickname" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="avatar" property="avatar" />
        <result column="gender" property="gender" />
        <result column="birthday" property="birthday" />
        <result column="city" property="city" />
        <result column="address" property="address" />
        <result column="bio" property="bio" />
        <result column="status" property="status" />
        <result column="auth_status" property="authStatus" />
        <result column="real_name" property="realName" />
        <result column="id_card" property="idCard" />
        <result column="credit_score" property="creditScore" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="last_login_ip" property="lastLoginIp" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, nickname, phone, email, avatar, gender, birthday, city, address, bio, 
        status, auth_status, real_name, id_card, credit_score, last_login_time, last_login_ip,
        create_time, update_time, create_by, update_by, deleted, version
    </sql>

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (username LIKE CONCAT('%', #{keyword}, '%') 
                 OR nickname LIKE CONCAT('%', #{keyword}, '%')
                 OR phone LIKE CONCAT('%', #{keyword}, '%')
                 OR real_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>

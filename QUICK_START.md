# 🚀 快速启动指南

## ❗ 重要：环境配置问题解决

### 问题1: JDK环境配置
**错误信息**: "No compiler is provided in this environment. Perhaps you are running on a JRE rather than a JDK?"

**解决方案**:
1. **确认JDK安装**: 确保安装的是JDK而不是JRE
2. **配置JAVA_HOME**: 
   ```bash
   # Windows
   set JAVA_HOME=C:\Program Files\Java\jdk-17
   set PATH=%JAVA_HOME%\bin;%PATH%
   
   # Linux/Mac
   export JAVA_HOME=/usr/lib/jvm/java-17-openjdk
   export PATH=$JAVA_HOME/bin:$PATH
   ```
3. **验证配置**:
   ```bash
   java -version
   javac -version
   echo $JAVA_HOME  # Linux/Mac
   echo %JAVA_HOME% # Windows
   ```

### 问题2: Maven配置
**确保Maven使用正确的JDK**:
```bash
mvn -version
```
应该显示JDK路径而不是JRE路径。

## 🔧 修复后的启动步骤

### 1. 环境检查
```bash
# 检查JDK
java -version
javac -version

# 检查Maven
mvn -version

# 检查基础服务
# MySQL: localhost:3306
# Redis: localhost:6379  
# Nacos: localhost:8848
```

### 2. 编译项目
```bash
# 清理并编译整个项目
mvn clean compile

# 如果编译成功，安装到本地仓库
mvn clean install -DskipTests
```

### 3. 启动服务

#### 方法一：IDE启动（推荐）
在IDE中直接运行各个服务的主类：
- `com.cty.gateway.GatewayApplication` (端口8080)
- `com.cty.admin.AdminApplication` (端口8088)
- `com.cty.user.UserApplication` (端口8082)

#### 方法二：命令行启动
```bash
# 启动网关服务
mvn spring-boot:run -pl cty-gateway

# 启动管理后台服务  
mvn spring-boot:run -pl cty-admin

# 启动用户服务
mvn spring-boot:run -pl cty-user
```

### 4. 验证启动
```bash
# 检查服务健康状态
curl http://localhost:8080/actuator/health  # 网关
curl http://localhost:8088/actuator/health  # 管理后台
curl http://localhost:8082/actuator/health  # 用户服务
```

### 5. 访问应用
- **API网关**: http://localhost:8080
- **管理后台前端**: http://localhost:3000 (需要启动前端项目)
- **Swagger文档**: http://localhost:8088/doc.html

## 🛠️ 常见问题解决

### 编译失败
1. **检查JDK版本**: 确保使用JDK 8+
2. **清理缓存**: `mvn clean`
3. **重新下载依赖**: `mvn dependency:resolve`

### 启动失败
1. **检查端口占用**: `netstat -an | findstr :8080`
2. **检查基础服务**: MySQL、Redis、Nacos是否启动
3. **查看日志**: 检查控制台输出的错误信息

### Nacos配置问题
确保每个服务的application.yml中都有：
```yaml
spring:
  config:
    import:
      - optional:nacos:服务名.yml
```

## 📋 服务启动顺序建议

1. **基础服务**: MySQL → Redis → Nacos
2. **核心服务**: Gateway → User → Admin
3. **业务服务**: Order → Payment → Message → Location → File
4. **前端应用**: Admin Web

## 🔍 调试技巧

### 查看详细错误
```bash
mvn clean compile -X  # 详细日志
mvn clean compile -e  # 错误堆栈
```

### IDE配置
1. **导入项目**: 作为Maven项目导入
2. **JDK配置**: 项目设置中指定正确的JDK
3. **Maven配置**: 指定正确的Maven和settings.xml

## 📞 获取帮助

如果遇到问题：
1. 检查错误日志
2. 确认环境配置
3. 参考本文档的解决方案
4. 联系开发团队

---

**重要提示**: 确保在正确配置JDK环境后再进行编译和启动操作！

# CTY-Message 消息服务

## 📋 模块概述
消息服务提供平台的实时通讯功能，包括即时聊天、系统通知、消息推送等通讯相关服务。

## 🎯 核心功能

### 1. 即时通讯
- **实时聊天**: WebSocket双向通讯
- **多媒体消息**: 文字、图片、语音、视频
- **消息状态**: 发送、送达、已读状态
- **离线消息**: 离线消息存储和推送

### 2. 会话管理
- **会话创建**: 自动创建用户间会话
- **会话列表**: 显示所有聊天会话
- **未读统计**: 实时未读消息计数
- **会话置顶**: 重要会话置顶功能

### 3. 系统通知
- **订单通知**: 订单状态变更通知
- **系统公告**: 平台重要公告推送
- **活动通知**: 营销活动消息推送
- **安全提醒**: 账户安全相关提醒

### 4. 消息推送
- **实时推送**: WebSocket实时推送
- **离线推送**: 第三方推送服务
- **推送策略**: 智能推送时间选择
- **推送统计**: 推送效果数据分析

## 💬 消息类型

### 聊天消息类型
- **文本消息**: 普通文字聊天
- **图片消息**: 图片分享
- **语音消息**: 语音通话记录
- **视频消息**: 视频通话记录
- **位置消息**: 地理位置分享
- **系统消息**: 系统自动消息

### 通知消息类型
- **订单通知**: 新订单、状态变更
- **支付通知**: 支付成功、退款
- **评价通知**: 收到新评价
- **关注通知**: 新粉丝关注
- **系统通知**: 平台公告

## 📊 数据模型

### 消息实体
```java
public class Message extends BaseEntity {
    private String conversationId;   // 会话ID
    private Long senderId;           // 发送者ID
    private Long receiverId;         // 接收者ID
    private Integer messageType;     // 消息类型
    private String content;          // 消息内容
    private String mediaUrl;         // 媒体文件URL
    private Integer isRead;          // 是否已读
    private LocalDateTime readTime;  // 阅读时间
    // ... 其他字段
}
```

### 会话实体
```java
public class Conversation extends BaseEntity {
    private String conversationId;      // 会话唯一标识
    private Long user1Id;               // 用户1ID
    private Long user2Id;               // 用户2ID
    private Long lastMessageId;         // 最后一条消息ID
    private LocalDateTime lastMessageTime; // 最后消息时间
    private Integer user1UnreadCount;   // 用户1未读数
    private Integer user2UnreadCount;   // 用户2未读数
    // ... 其他字段
}
```

## 🔧 核心接口

### 1. 发送消息
```http
POST /message/send
Content-Type: application/json

{
    "receiverId": 123,
    "messageType": 1,
    "content": "你好，我想咨询一下服务详情"
}
```

### 2. 获取会话列表
```http
GET /message/conversations?userId=123
```

### 3. 获取聊天记录
```http
GET /message/history?conversationId=conv_123&page=1&size=20
```

### 4. 标记消息已读
```http
PUT /message/read
Content-Type: application/json

{
    "conversationId": "conv_123",
    "userId": 123
}
```

### 5. 获取未读消息数
```http
GET /message/unread/count?userId=123
```

## 🌐 WebSocket通讯

### 连接建立
```javascript
// 客户端连接
const socket = new SockJS('/ws');
const stompClient = Stomp.over(socket);

stompClient.connect({}, function(frame) {
    console.log('Connected: ' + frame);
    
    // 订阅个人消息
    stompClient.subscribe('/user/queue/messages', function(message) {
        handleMessage(JSON.parse(message.body));
    });
});
```

### 消息发送
```javascript
// 发送消息
stompClient.send("/app/chat", {}, JSON.stringify({
    'receiverId': 123,
    'content': 'Hello World',
    'messageType': 1
}));
```

### 消息接收
```javascript
// 处理接收到的消息
function handleMessage(message) {
    console.log('Received: ', message);
    // 更新UI显示新消息
    updateChatUI(message);
}
```

## 📱 推送服务集成

### 极光推送配置
```yaml
jpush:
  app-key: your-app-key
  master-secret: your-master-secret
  production: false
```

### 推送消息格式
```json
{
    "platform": "all",
    "audience": {
        "alias": ["user_123"]
    },
    "notification": {
        "alert": "您有新消息",
        "android": {
            "title": "同城互助",
            "alert": "您收到一条新消息"
        },
        "ios": {
            "alert": "您收到一条新消息",
            "badge": 1
        }
    }
}
```

## 🔄 消息流程

### 实时消息流程
1. 用户发送消息
2. 服务器接收处理
3. 存储到数据库
4. 推送给接收方
5. 更新会话信息
6. 返回发送结果

### 离线消息流程
1. 检测用户在线状态
2. 离线用户消息存储
3. 用户上线时推送
4. 标记消息已送达
5. 更新未读计数

## 📈 统计分析
- 消息发送量统计
- 用户活跃度分析
- 消息类型分布
- 推送到达率
- 用户互动频率

## 🛡️ 安全机制

### 1. 内容安全
- 敏感词过滤
- 图片内容检测
- 垃圾消息识别
- 举报处理机制

### 2. 隐私保护
- 消息加密传输
- 阅后即焚功能
- 消息撤回功能
- 黑名单机制

## 🚀 技术特性
- **端口**: 8085
- **协议**: WebSocket + STOMP
- **数据库**: MySQL (city_help_platform)
- **缓存**: Redis (database: 4)
- **消息队列**: RabbitMQ
- **推送服务**: 极光推送/个推

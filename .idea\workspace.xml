<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1577732d-9917-4be9-ae8f-1815d62afa37" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\devtools\apache-maven-3.5.4" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\devtools\apache-maven-3.5.4\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="30b3hvm7aBbH6BjZZeib7Fasayt" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.cty-admin [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.cty-gateway [clean].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;Spring Boot.AdminApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.GatewayApplication.executor&quot;: &quot;Debug&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Program Files\\JetBrains\\IntelliJ IDEA 2025.1.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.AdminApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="city-help-platform" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="city-help-platform" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="AdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cty-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cty.admin.AdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cty-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cty.auth.AuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="FileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cty-file" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cty.file.FileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cty-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cty.gateway.GatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LocationApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cty-location" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cty.location.LocationApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MessageApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cty-message" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cty.message.MessageApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OrderApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cty-order" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cty.order.OrderApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PaymentApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cty-payment" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cty.payment.PaymentApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cty-user" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cty.user.UserApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="1577732d-9917-4be9-ae8f-1815d62afa37" name="更改" comment="" />
      <created>1753882567483</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753882567483</updated>
      <workItem from="1753882568631" duration="1766000" />
      <workItem from="1753884344676" duration="1989000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>
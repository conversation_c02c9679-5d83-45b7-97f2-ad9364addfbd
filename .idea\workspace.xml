<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\devtools\apache-maven-3.5.4" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\devtools\apache-maven-3.5.4\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;
  }
}</component>
</project>
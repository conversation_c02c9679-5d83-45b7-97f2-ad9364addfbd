package com.cty.file.controller;

import com.cty.common.result.Result;
import com.cty.file.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件控制器
 */
@Tag(name = "文件管理", description = "文件上传下载相关接口")
@RestController
@RequestMapping("/file")
@RequiredArgsConstructor
public class FileController {
    
    private final FileService fileService;
    
    @Operation(summary = "上传文件")
    @PostMapping("/upload")
    public Result<String> uploadFile(@RequestParam("file") MultipartFile file,
                                   @RequestParam(value = "folder", defaultValue = "common") String folder) {
        String fileUrl = fileService.uploadFile(file, folder);
        return Result.success("上传成功", fileUrl);
    }
    
    @Operation(summary = "上传图片")
    @PostMapping("/upload/image")
    public Result<String> uploadImage(@RequestParam("file") MultipartFile file) {
        String fileUrl = fileService.uploadImage(file);
        return Result.success("上传成功", fileUrl);
    }
    
    @Operation(summary = "上传头像")
    @PostMapping("/upload/avatar")
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file) {
        String fileUrl = fileService.uploadAvatar(file);
        return Result.success("上传成功", fileUrl);
    }
    
    @Operation(summary = "上传身份证照片")
    @PostMapping("/upload/idcard")
    public Result<String> uploadIdCard(@RequestParam("file") MultipartFile file) {
        String fileUrl = fileService.uploadIdCard(file);
        return Result.success("上传成功", fileUrl);
    }
    
    @Operation(summary = "删除文件")
    @DeleteMapping("/delete")
    public Result<Boolean> deleteFile(@RequestParam String fileUrl) {
        boolean result = fileService.deleteFile(fileUrl);
        return Result.success("删除成功", result);
    }
    
    @Operation(summary = "获取文件访问URL")
    @GetMapping("/url")
    public Result<String> getFileUrl(@RequestParam String fileName) {
        String fileUrl = fileService.getFileUrl(fileName);
        return Result.success(fileUrl);
    }
    
    @Operation(summary = "检查文件是否存在")
    @GetMapping("/exists")
    public Result<Boolean> fileExists(@RequestParam String fileName) {
        boolean exists = fileService.fileExists(fileName);
        return Result.success(exists);
    }
}

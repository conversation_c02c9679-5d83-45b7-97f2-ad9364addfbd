-- 检查数据库表状态的脚本
USE city_help_platform;

-- 检查所有表
SHOW TABLES;

-- 检查用户表
SELECT '=== 用户表检查 ===' as info;
SELECT COUNT(*) as user_count FROM user WHERE deleted = 0;
SELECT status, COUNT(*) as count FROM user WHERE deleted = 0 GROUP BY status;

-- 检查管理员表
SELECT '=== 管理员表检查 ===' as info;
SELECT COUNT(*) as admin_count FROM admin_user WHERE deleted = 0;

-- 检查订单表是否存在
SELECT '=== 订单表检查 ===' as info;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '订单表存在'
        ELSE '订单表不存在'
    END as orders_table_status
FROM information_schema.tables 
WHERE table_schema = 'city_help_platform' 
AND table_name = 'orders';

-- 如果订单表存在，显示统计信息
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'city_help_platform' AND table_name = 'orders') > 0 
        THEN (SELECT COUNT(*) FROM orders WHERE deleted = 0)
        ELSE 0
    END as orders_count;

-- 显示表结构（如果存在）
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'city_help_platform' AND table_name = 'orders') > 0 
        THEN 'orders表存在，可以查看结构'
        ELSE 'orders表不存在，需要创建'
    END as table_info;

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var stdin_exports = {};
__export(stdin_exports, {
  default: () => stdin_default
});
module.exports = __toCommonJS(stdin_exports);
var stdin_default = {
  name: "\u038C\u03BD\u03BF\u03BC\u03B1",
  tel: "\u03A4\u03B7\u03BB\u03AD\u03C6\u03C9\u03BD\u03BF",
  save: "\u0391\u03C0\u03BF\u03B8\u03AE\u03BA\u03B5\u03C5\u03C3\u03B7",
  clear: "\u03A3\u03B1\u03C6\u03AE",
  cancel: "\u0391\u03BA\u03CD\u03C1\u03C9\u03C3\u03B7",
  confirm: "\u0395\u03C0\u03B9\u03B2\u03B5\u03B2\u03B1\u03AF\u03C9\u03C3\u03B7",
  delete: "\u0394\u03B9\u03B1\u03B3\u03C1\u03B1\u03C6\u03AE",
  loading: "\u03A6\u03CC\u03C1\u03C4\u03C9\u03C3\u03B7...",
  noCoupon: "\u03A7\u03C9\u03C1\u03AF\u03C2 \u03BA\u03BF\u03C5\u03C0\u03CC\u03BD\u03B9\u03B1",
  nameEmpty: "\u03A0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03CE \u03C3\u03C5\u03BC\u03C0\u03BB\u03B7\u03C1\u03CE\u03C3\u03C4\u03B5 \u03C4\u03BF \u03CC\u03BD\u03BF\u03BC\u03B1",
  addContact: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03B5\u03C0\u03B1\u03C6\u03AE\u03C2",
  telInvalid: "\u0391\u03C1\u03B9\u03B8\u03BC\u03CC\u03C2 \u03C4\u03B7\u03BB\u03B5\u03C6\u03CE\u03BD\u03BF\u03C5 \u03BC\u03B5 \u03B5\u03C3\u03C6\u03B1\u03BB\u03BC\u03AD\u03BD\u03B7 \u03BC\u03BF\u03C1\u03C6\u03AE",
  vanCalendar: {
    end: "\u03A4\u03AD\u03BB\u03BF\u03C2",
    start: "\u0388\u03BD\u03B1\u03C1\u03BE\u03B7",
    title: "\u0397\u03BC\u03B5\u03C1\u03BF\u03BB\u03CC\u03B3\u03B9\u03BF",
    weekdays: [
      "\u039A\u03C5\u03C1\u03B9\u03B1\u03BA\u03AE",
      "\u0394\u03B5\u03C5\u03C4\u03AD\u03C1\u03B1",
      "\u03A4\u03C1\u03AF\u03C4\u03B7",
      "\u03A4\u03B5\u03C4\u03AC\u03C1\u03C4\u03B7",
      "\u03A0\u03AD\u03BC\u03C0\u03C4\u03B7",
      "\u03A0\u03B1\u03C1\u03B1\u03C3\u03BA\u03B5\u03C5\u03AE",
      "\u03A3\u03AC\u03B2\u03B2\u03B1\u03C4\u03BF"
    ],
    monthTitle: (year, month) => `${year}/${month}`,
    rangePrompt: (maxRange) => `\u0395\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03CC\u03C7\u03B9 \u03C0\u03B5\u03C1\u03B9\u03C3\u03C3\u03CC\u03C4\u03B5\u03C1\u03B5\u03C2 \u03B1\u03C0\u03CC ${maxRange} \u03B7\u03BC\u03AD\u03C1\u03B5\u03C2`
  },
  vanCascader: {
    select: "\u0395\u03C0\u03B9\u03BB\u03BF\u03B3\u03AE"
  },
  vanPagination: {
    prev: "\u03A0\u03C1\u03BF\u03B7\u03B3\u03BF\u03CD\u03BC\u03B5\u03BD\u03BF",
    next: "\u0395\u03C0\u03CC\u03BC\u03B5\u03BD\u03BF"
  },
  vanPullRefresh: {
    pulling: "\u03A4\u03C1\u03B1\u03B2\u03AE\u03BE\u03C4\u03B5 \u03B3\u03B9\u03B1 \u03B1\u03BD\u03B1\u03BD\u03AD\u03C9\u03C3\u03B7...",
    loosing: "\u03A7\u03B1\u03BB\u03B1\u03C1\u03AC \u03B3\u03B9\u03B1 \u03B1\u03BD\u03B1\u03BD\u03AD\u03C9\u03C3\u03B7..."
  },
  vanSubmitBar: {
    label: "\u03A3\u03CD\u03BD\u03BF\u03BB\u03BF:"
  },
  vanCoupon: {
    unlimited: "\u0391\u03C0\u03B5\u03C1\u03B9\u03CC\u03C1\u03B9\u03C3\u03C4\u03BF",
    discount: (discount) => `${discount * 10}% \u03AD\u03BA\u03C0\u03C4\u03C9\u03C3\u03B7`,
    condition: (condition) => `\u03A4\u03BF\u03C5\u03BB\u03AC\u03C7\u03B9\u03C3\u03C4\u03BF\u03BD ${condition}`
  },
  vanCouponCell: {
    title: "\u039A\u03BF\u03C5\u03C0\u03CC\u03BD\u03B9",
    count: (count) => `\u0388\u03C7\u03B5\u03C4\u03B5 ${count} \u03BA\u03BF\u03C5\u03C0\u03CC\u03BD\u03B9\u03B1`
  },
  vanCouponList: {
    exchange: "\u0391\u03BD\u03C4\u03B1\u03BB\u03BB\u03B1\u03B3\u03AE",
    close: "\u039A\u03BB\u03B5\u03AF\u03C3\u03B9\u03BC\u03BF",
    enable: "\u0394\u03B9\u03B1\u03B8\u03AD\u03C3\u03B9\u03BC\u03BF",
    disabled: "\u039C\u03B7 \u03B4\u03B9\u03B1\u03B8\u03AD\u03C3\u03B9\u03BC\u03BF",
    placeholder: "\u039A\u03C9\u03B4\u03B9\u03BA\u03CC\u03C2 \u03BA\u03BF\u03C5\u03C0\u03BF\u03BD\u03B9\u03BF\u03CD"
  },
  vanAddressEdit: {
    area: "\u03A0\u03B5\u03C1\u03B9\u03BF\u03C7\u03AE",
    postal: "\u03A4\u03B1\u03C7\u03C5\u03B4\u03C1\u03BF\u03BC\u03B5\u03AF\u03BF",
    areaEmpty: "\u03A0\u03B1\u03C1\u03B1\u03BA\u03B1\u03BB\u03CE \u03B5\u03C0\u03B9\u03BB\u03AD\u03BE\u03C4\u03B5 \u03BC\u03B9\u03B1 \u03C0\u03B5\u03C1\u03B9\u03BF\u03C7\u03AE \u03BB\u03AE\u03C8\u03B7\u03C2",
    addressEmpty: "\u0397 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7 \u03B4\u03B5\u03BD \u03BC\u03C0\u03BF\u03C1\u03B5\u03AF \u03BD\u03B1 \u03B5\u03AF\u03BD\u03B1\u03B9 \u03BA\u03B5\u03BD\u03AE",
    postalEmpty: "\u039B\u03AC\u03B8\u03BF\u03C2 \u03C4\u03B1\u03C7\u03C5\u03B4\u03C1\u03BF\u03BC\u03B9\u03BA\u03CC\u03C2 \u03BA\u03CE\u03B4\u03B9\u03BA\u03B1\u03C2",
    addressDetail: "\u0394\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7",
    defaultAddress: "\u039F\u03C1\u03B9\u03C3\u03BC\u03CC\u03C2 \u03C9\u03C2 \u03C0\u03C1\u03BF\u03B5\u03C0\u03B9\u03BB\u03B5\u03B3\u03BC\u03AD\u03BD\u03B7 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7"
  },
  vanAddressList: {
    add: "\u03A0\u03C1\u03BF\u03C3\u03B8\u03AE\u03BA\u03B7 \u03BD\u03AD\u03B1\u03C2 \u03B4\u03B9\u03B5\u03CD\u03B8\u03C5\u03BD\u03C3\u03B7\u03C2"
  }
};

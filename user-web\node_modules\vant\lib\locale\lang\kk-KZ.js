var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var stdin_exports = {};
__export(stdin_exports, {
  default: () => stdin_default
});
module.exports = __toCommonJS(stdin_exports);
var stdin_default = {
  name: "\u0410\u0442\u044B",
  tel: "\u0422\u0435\u043B\u0435\u0444\u043E\u043D",
  save: "\u0421\u0430\u049B\u0442\u0430\u0443",
  clear: "\u0422\u0430\u0437\u0430\u0440\u0442\u0443",
  cancel: "\u0411\u0430\u0441\u0442\u0430\u0440\u0442\u0443",
  confirm: "\u0420\u0430\u0441\u0442\u0430\u0443",
  delete: "\u0416\u043E\u044E",
  loading: "\u0416\u04AF\u043A\u0442\u0435\u043B\u0443\u0434\u0435...",
  noCoupon: "\u041A\u0443\u043F\u043E\u043D\u0434\u0430\u0440 \u0436\u043E\u049B",
  nameEmpty: "\u0410\u0442\u044B-\u0436\u04E9\u043D\u0456\u04A3\u0456\u0437\u0434\u0456 \u0442\u043E\u043B\u0442\u044B\u0440\u044B\u04A3\u044B\u0437",
  addContact: "\u041A\u043E\u043D\u0442\u0430\u043A\u0442\u0456 \u049B\u043E\u0441\u0443",
  telInvalid: "\u0414\u04B1\u0440\u044B\u0441 \u0442\u0435\u043B\u0435\u0444\u043E\u043D \u043D\u04E9\u043C\u0456\u0440\u0456\u043D \u0442\u043E\u043B\u0442\u044B\u0440\u044B\u04A3\u044B\u0437",
  vanCalendar: {
    end: "\u0410\u044F\u049B\u0442\u0430\u043B\u0443 \u0443\u0430\u049B\u044B\u0442\u044B",
    start: "\u0411\u0430\u0441\u0442\u0430\u0443 \u0443\u0430\u049B\u044B\u0442\u044B",
    title: "\u041A\u04AF\u043D\u0434\u0456 \u0442\u0430\u04A3\u0434\u0430\u0443",
    weekdays: ["\u0416\u0421", "\u0414\u0421", "\u0421\u0421", "\u0421\u0420", "\u0411\u0421", "\u0416\u041C", "\u0421\u0411"],
    monthTitle: (year, month) => {
      const monthNames = [
        "\u049A\u0430\u04A3\u0442\u0430\u0440",
        "\u0410\u049B\u043F\u0430\u043D",
        "\u041D\u0430\u0443\u0440\u044B\u0437",
        "\u0421\u04D9\u0443\u0456\u0440",
        "\u041C\u0430\u043C\u044B\u0440",
        "\u041C\u0430\u0443\u0441\u044B\u043C",
        "\u0428\u0456\u043B\u0434\u0435",
        "\u0422\u0430\u043C\u044B\u0437",
        "\u049A\u044B\u0440\u043A\u04AF\u0439\u0435\u043A",
        "\u049A\u0430\u0437\u0430\u043D",
        "\u049A\u0430\u0440\u0430\u0448\u0430",
        "\u0416\u0435\u043B\u0442\u043E\u049B\u0441\u0430\u043D"
      ];
      return `${year} ${monthNames[month - 1]}`;
    },
    rangePrompt: (maxRange) => `\u0422\u0435\u043A ${maxRange} \u043A\u04AF\u043D\u0433\u0435 \u0434\u0435\u0439\u0456\u043D \u0442\u0430\u04A3\u0434\u0430\u0439 \u0430\u043B\u0430\u0441\u044B\u0437`
  },
  vanCascader: {
    select: "\u0422\u0430\u04A3\u0434\u0430\u04A3\u044B\u0437"
  },
  vanPagination: {
    prev: "\u0410\u0440\u0442\u049B\u0430",
    next: "\u0410\u043B\u0493\u0430"
  },
  vanPullRefresh: {
    pulling: "\u0416\u0430\u04A3\u0430\u0440\u0442\u0443 \u04AF\u0448\u0456\u043D \u0442\u0430\u0440\u0442\u044B\u04A3\u044B\u0437...",
    loosing: "\u0416\u0430\u04A3\u0430\u0440\u0442\u0443 \u04AF\u0448\u0456\u043D \u0436\u0456\u0431\u0435\u0440\u0456\u04A3\u0456\u0437..."
  },
  vanSubmitBar: {
    label: "\u0416\u0430\u043B\u043F\u044B:"
  },
  vanCoupon: {
    unlimited: "\u0422\u0430\u043B\u0430\u043F \u0436\u043E\u049B",
    discount: (discount) => `${discount * 10}% \u0436\u0435\u04A3\u0456\u043B\u0434\u0456\u043A`,
    condition: (condition) => `${condition} \u0442\u0435\u04A3\u0433\u0435\u0433\u0435 \u0436\u0435\u0442\u043A\u0435\u043D\u0434\u0435 \u049B\u043E\u043B \u0436\u0435\u0442\u0456\u043C\u0434\u0456`
  },
  vanCouponCell: {
    title: "\u041A\u0443\u043F\u043E\u043D",
    count: (count) => `${count} \u049B\u043E\u043B \u0436\u0435\u0442\u0456\u043C\u0434\u0456 \u043A\u0443\u043F\u043E\u043D`
  },
  vanCouponList: {
    exchange: "\u0410\u0439\u044B\u0440\u0431\u0430\u0441\u0442\u0430\u0443",
    close: "\u049A\u043E\u043B\u0434\u0430\u043D\u0431\u0430\u0443",
    enable: "\u049A\u043E\u043B \u0436\u0435\u0442\u0456\u043C\u0434\u0456",
    disabled: "\u049A\u043E\u043B\u0436\u0435\u0442\u0456\u043C\u0441\u0456\u0437",
    placeholder: "\u0416\u0435\u04A3\u0456\u043B\u0434\u0456\u043A \u043A\u043E\u0434\u044B\u043D \u0435\u043D\u0433\u0456\u0437\u0456\u04A3\u0456\u0437"
  },
  vanAddressEdit: {
    area: "\u0410\u0443\u043C\u0430\u049B",
    areaEmpty: "\u0410\u0439\u043C\u0430\u049B\u0442\u044B \u0442\u0430\u04A3\u0434\u0430\u04A3\u044B\u0437",
    addressEmpty: "\u0422\u043E\u043B\u044B\u049B \u043C\u0435\u043A\u0435\u043D-\u0436\u0430\u0439\u0434\u044B \u0442\u043E\u043B\u0442\u044B\u0440\u044B\u04A3\u044B\u0437",
    addressDetail: "\u041C\u0435\u043A\u0435\u043D\u0436\u0430\u0439",
    defaultAddress: "\u04D8\u0434\u0435\u043F\u043A\u0456 \u0436\u0435\u0442\u043A\u0456\u0437\u0443 \u043C\u0435\u043A\u0435\u043D\u0436\u0430\u0439\u044B \u0440\u0435\u0442\u0456\u043D\u0434\u0435 \u043E\u0440\u043D\u0430\u0442\u0443"
  },
  vanAddressList: {
    add: "\u041C\u0435\u043A\u0435\u043D\u0436\u0430\u0439 \u049B\u043E\u0441\u0443"
  }
};

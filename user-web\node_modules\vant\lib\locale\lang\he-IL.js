var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var stdin_exports = {};
__export(stdin_exports, {
  default: () => stdin_default
});
module.exports = __toCommonJS(stdin_exports);
var stdin_default = {
  name: "\u05E9\u05DD",
  tel: "\u05D8\u05DC\u05E4\u05D5\u05DF",
  save: "\u05E9\u05DE\u05D5\u05E8",
  clear: "\u05D1\u05E8\u05D5\u05E8",
  cancel: "\u05D1\u05D9\u05D8\u05D5\u05DC",
  confirm: "\u05D0\u05D9\u05E9\u05D5\u05E8",
  delete: "\u05DE\u05D7\u05D9\u05E7\u05D4",
  loading: "\u05D8\u05D5\u05E2\u05DF...",
  noCoupon: "\u05D0\u05D9\u05DF \u05E7\u05D5\u05E4\u05D5\u05E0\u05D9\u05DD",
  nameEmpty: "\u05D0\u05E0\u05D0 \u05DE\u05DC\u05D0 \u05D0\u05EA \u05D4\u05E9\u05D3\u05D4",
  addContact: "\u05D4\u05D5\u05E1\u05E3 \u05D0\u05D9\u05E9-\u05E7\u05E9\u05E8",
  telInvalid: "\u05DE\u05E1\u05E4\u05E8 \u05D8\u05DC\u05E4\u05D5\u05DF \u05E9\u05D2\u05D5\u05D9",
  vanCalendar: {
    end: "\u05E1\u05D5\u05E3",
    start: "\u05D4\u05EA\u05D7\u05DC\u05D4",
    title: "\u05DC\u05D5\u05D7 \u05E9\u05E0\u05D4",
    weekdays: ["\u05E8\u05D0\u05E9\u05D5\u05DF", "\u05E9\u05E0\u05D9", "\u05E9\u05DC\u05D9\u05E9\u05D9", "\u05E8\u05D1\u05D9\u05E2\u05D9", "\u05D7\u05DE\u05D9\u05E9\u05D9", "\u05E9\u05D9\u05E9\u05D9", "\u05E9\u05D1\u05EA"],
    monthTitle: (year, month) => `${year}/${month}`,
    rangePrompt: (maxRange) => ` \u05D1\u05D7\u05E8 \u05DC\u05D0 \u05D9\u05D5\u05EA\u05E8 \u05DE ${maxRange} \u05D9\u05DE\u05D9\u05DD `
  },
  vanCascader: {
    select: "\u05D1\u05D7\u05E8"
  },
  vanPagination: {
    prev: "\u05D4\u05E7\u05D5\u05D3\u05DD",
    next: "\u05D4\u05D1\u05D0"
  },
  vanPullRefresh: {
    pulling: "\u05D2\u05E8\u05D5\u05E8 \u05DB\u05D3\u05D9 \u05DC\u05E8\u05E2\u05E0\u05DF",
    loosing: "\u05E9\u05D7\u05E8\u05E8 \u05DB\u05D3\u05D9 \u05DC\u05E8\u05E2\u05E0\u05DF"
  },
  vanSubmitBar: {
    label: "\u05E1\u05DA \u05D4\u05DB\u05DC:"
  },
  vanCoupon: {
    unlimited: "\u05DC\u05DC\u05D0 \u05D4\u05D2\u05D1\u05DC\u05D4",
    discount: (discount) => `${discount * 10}% \u05D4\u05E0\u05D7\u05D4`,
    condition: (condition) => ` \u05DC\u05E4\u05D7\u05D5\u05EA ${condition}`
  },
  vanCouponCell: {
    title: "\u05E7\u05D5\u05E4\u05D5\u05DF",
    count: (count) => ` \u05D9\u05E9 \u05DC\u05DA ${count} \u05E7\u05D5\u05E4\u05D5\u05E0\u05D9\u05DD `
  },
  vanCouponList: {
    exchange: "\u05D4\u05D7\u05DC\u05E4\u05D4",
    close: "\u05E1\u05D2\u05D9\u05E8\u05D4",
    enable: "\u05D6\u05DE\u05D9\u05E0\u05D9\u05DD",
    disabled: "\u05DC\u05D0 \u05D6\u05DE\u05D9\u05E0\u05D9\u05DD",
    placeholder: "\u05E7\u05D5\u05D3 \u05E7\u05D5\u05E4\u05D5\u05DF"
  },
  vanAddressEdit: {
    area: "\u05D0\u05D9\u05D6\u05D5\u05E8",
    postal: "\u05DE\u05D9\u05E7\u05D5\u05D3",
    areaEmpty: "\u05D0\u05E0\u05D0 \u05D1\u05D7\u05E8 \u05D0\u05D9\u05D6\u05D5\u05E8 \u05E7\u05D1\u05DC\u05D4",
    addressEmpty: "\u05D9\u05E9 \u05DC\u05DE\u05DC\u05D0 \u05DB\u05EA\u05D5\u05D1\u05EA",
    postalEmpty: "\u05D8\u05E2\u05D5\u05EA \u05D1\u05DE\u05D9\u05E7\u05D5\u05D3",
    addressDetail: "\u05DB\u05EA\u05D5\u05D1\u05EA",
    defaultAddress: "\u05D4\u05D2\u05D3\u05E8 \u05DB\u05DB\u05EA\u05D5\u05D1\u05EA \u05D1\u05E8\u05D9\u05E8\u05EA \u05DE\u05D7\u05D3\u05E9"
  },
  vanAddressList: {
    add: "\u05D4\u05D5\u05E1\u05E4\u05EA \u05DB\u05EA\u05D5\u05D1\u05EA \u05D7\u05D3\u05E9\u05D4"
  }
};

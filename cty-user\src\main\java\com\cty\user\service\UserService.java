package com.cty.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cty.user.entity.User;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {
    
    /**
     * 根据用户名查询用户
     */
    User getByUsername(String username);
    
    /**
     * 根据手机号查询用户
     */
    User getByPhone(String phone);
    
    /**
     * 用户注册
     */
    User register(User user);
    
    /**
     * 用户登录
     */
    User login(String username, String password);
    
    /**
     * 更新用户信息
     */
    boolean updateUserInfo(User user);
    
    /**
     * 实名认证
     */
    boolean realNameAuth(Long userId, String realName, String idCard, String idCardFront, String idCardBack);
    
    /**
     * 更新用户位置
     */
    boolean updateLocation(Long userId, String province, String city, String district, String address, 
                          java.math.BigDecimal longitude, java.math.BigDecimal latitude);
}

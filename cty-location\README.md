# CTY-Location 位置服务

## 📋 模块概述
位置服务负责处理地理位置相关的计算和服务，为平台提供基于位置的智能匹配和导航功能。

## 🎯 核心功能

### 1. 地理位置计算
- **距离计算**: 两点间直线距离和实际距离
- **方位计算**: 计算两点间的方位角度
- **区域判断**: 判断点是否在指定区域内
- **路径规划**: 最优路径计算

### 2. 地址解析服务
- **地理编码**: 地址转换为经纬度坐标
- **逆地理编码**: 坐标转换为详细地址
- **地址标准化**: 地址格式统一处理
- **POI搜索**: 兴趣点搜索功能

### 3. 附近搜索
- **附近用户**: 搜索指定范围内的用户
- **附近需求**: 搜索附近的互助需求
- **热点区域**: 分析需求热点分布
- **服务覆盖**: 计算服务覆盖范围

### 4. 地图服务集成
- **高德地图**: 集成高德地图API
- **百度地图**: 集成百度地图API
- **腾讯地图**: 集成腾讯地图API
- **谷歌地图**: 国外服务支持

## 🗺️ 地理计算算法

### 距离计算公式
```java
/**
 * 计算两点间距离（米）
 * 使用Haversine公式
 */
public double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    double R = 6371000; // 地球半径（米）
    double dLat = Math.toRadians(lat2 - lat1);
    double dLng = Math.toRadians(lng2 - lng1);
    
    double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
               Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
               Math.sin(dLng/2) * Math.sin(dLng/2);
    
    double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}
```

### 附近搜索算法
```java
/**
 * 搜索指定范围内的点
 * 使用空间索引优化
 */
public List<Point> findNearbyPoints(double lat, double lng, double radius) {
    // 计算搜索边界
    double latRange = radius / 111000; // 纬度1度约111km
    double lngRange = radius / (111000 * Math.cos(Math.toRadians(lat)));
    
    // 边界框搜索
    return pointRepository.findByLatitudeBetweenAndLongitudeBetween(
        lat - latRange, lat + latRange,
        lng - lngRange, lng + lngRange
    ).stream()
    .filter(point -> calculateDistance(lat, lng, point.getLat(), point.getLng()) <= radius)
    .collect(Collectors.toList());
}
```

## 📊 数据模型

### 位置信息
```java
public class LocationInfo {
    private BigDecimal latitude;     // 纬度
    private BigDecimal longitude;    // 经度
    private String province;         // 省份
    private String city;            // 城市
    private String district;        // 区县
    private String address;         // 详细地址
    private String poiName;         // POI名称
    // ... 其他字段
}
```

### 地址信息
```java
public class AddressInfo {
    private String province;        // 省份
    private String city;           // 城市
    private String district;       // 区县
    private String street;         // 街道
    private String address;        // 详细地址
    private String adcode;         // 行政区划代码
    private String citycode;       // 城市代码
    // ... 其他字段
}
```

## 🔧 核心接口

### 1. 计算距离
```http
GET /location/distance?lat1=39.908823&lng1=116.397470&lat2=39.918823&lng2=116.407470
```

### 2. 地理编码
```http
GET /location/geocode?address=北京市朝阳区三里屯
```

### 3. 逆地理编码
```http
GET /location/regeocode?latitude=39.908823&longitude=116.397470
```

### 4. 附近用户搜索
```http
GET /location/nearby/users?latitude=39.908823&longitude=116.397470&radius=5000
```

### 5. 附近需求搜索
```http
GET /location/nearby/requests?latitude=39.908823&longitude=116.397470&radius=3000&category=1
```

## 🗺️ 第三方地图API集成

### 高德地图配置
```yaml
amap:
  key: your-amap-key
  url: https://restapi.amap.com
  services:
    geocode: /v3/geocode/geo
    regeocode: /v3/geocode/regeo
    distance: /v3/distance
    direction: /v3/direction/driving
```

### 百度地图配置
```yaml
baidu:
  ak: your-baidu-ak
  url: https://api.map.baidu.com
  services:
    geocode: /geocoding/v3
    regeocode: /reverse_geocoding/v3
    direction: /direction/v2/driving
```

## 🎯 业务场景

### 智能匹配
```java
/**
 * 基于位置的需求匹配
 */
public List<MatchResult> matchRequests(Long userId, double maxDistance) {
    User user = userService.getById(userId);
    
    // 搜索附近需求
    List<HelpRequest> nearbyRequests = findNearbyRequests(
        user.getLatitude(), user.getLongitude(), maxDistance
    );
    
    // 计算匹配度
    return nearbyRequests.stream()
        .map(request -> {
            double distance = calculateDistance(
                user.getLatitude(), user.getLongitude(),
                request.getLatitude(), request.getLongitude()
            );
            double score = calculateMatchScore(user, request, distance);
            return new MatchResult(request, distance, score);
        })
        .sorted(Comparator.comparing(MatchResult::getScore).reversed())
        .collect(Collectors.toList());
}
```

### 热点分析
```java
/**
 * 分析需求热点区域
 */
public List<HotSpot> analyzeHotSpots(String city) {
    List<HelpRequest> requests = requestService.getByCity(city);
    
    // 网格化分析
    Map<String, List<HelpRequest>> grid = requests.stream()
        .collect(Collectors.groupingBy(this::getGridKey));
    
    return grid.entrySet().stream()
        .filter(entry -> entry.getValue().size() >= MIN_REQUESTS)
        .map(entry -> new HotSpot(
            parseGridKey(entry.getKey()),
            entry.getValue().size(),
            calculateHotLevel(entry.getValue())
        ))
        .sorted(Comparator.comparing(HotSpot::getLevel).reversed())
        .collect(Collectors.toList());
}
```

## 📈 性能优化

### 1. 空间索引
- 使用GeoHash编码
- MySQL空间索引
- Redis GEO命令

### 2. 缓存策略
- 地址解析结果缓存
- 热点区域缓存
- 距离计算缓存

### 3. 批量处理
- 批量地理编码
- 批量距离计算
- 异步处理机制

## 🛡️ 数据安全
- 位置信息脱敏
- 精确度控制
- 隐私保护设置
- 位置共享权限

## 🚀 技术特性
- **端口**: 8086
- **缓存**: Redis (database: 5)
- **地图API**: 高德/百度/腾讯
- **空间计算**: PostGIS (可选)
- **性能优化**: 空间索引和缓存
